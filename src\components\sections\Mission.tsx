import { <PERSON>, Target, Heart, Gamepad2, <PERSON>, Zap } from 'lucide-react'

const Mission = () => {
  return (
    <section className="section-padding bg-dark-800/50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="font-tech text-3xl md:text-5xl font-bold text-white mb-6">
            Our <span className="bg-gradient-to-r from-robocell-yellow to-robocell-orange bg-clip-text text-transparent">Purpose</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Driven by passion, guided by innovation, and committed to excellence in robotics and automation! 🚀⚡
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Mission */}
          <div className="card text-center group">
            <div className="relative mb-6">
              <Target className="h-16 w-16 text-robocell-yellow mx-auto group-hover:animate-pulse" />
              <div className="absolute inset-0 h-16 w-16 text-robocell-yellow opacity-30 mx-auto group-hover:animate-ping" />
            </div>
            <h3 className="font-tech text-2xl font-bold text-white mb-4">Mission</h3>
            <p className="text-gray-300 leading-relaxed">
              To be the coolest robotics club at NIT Durgapur! 🤖 We turn classroom theory into
              epic robot builds, dominate competitions like Robocon, and create tech that makes
              people go "How did they even do that?!" 🔥
            </p>
          </div>

          {/* Vision */}
          <div className="card text-center group">
            <div className="relative mb-6">
              <Eye className="h-16 w-16 text-robocell-orange mx-auto group-hover:animate-pulse" />
              <div className="absolute inset-0 h-16 w-16 text-robocell-orange opacity-30 mx-auto group-hover:animate-ping" />
            </div>
            <h3 className="font-tech text-2xl font-bold text-white mb-4">Vision</h3>
            <p className="text-gray-300 leading-relaxed">
              To create a squad of robot-building legends who crush competitions, invent
              mind-blowing tech, and become the engineers everyone wants to hire! 🚀
              From campus to global stage - we're building the future! ⚡
            </p>
          </div>

          {/* Values */}
          <div className="card text-center group">
            <div className="relative mb-6">
              <Heart className="h-16 w-16 text-electric-amber mx-auto group-hover:animate-pulse" />
              <div className="absolute inset-0 h-16 w-16 text-electric-amber opacity-30 mx-auto group-hover:animate-ping" />
            </div>
            <h3 className="font-tech text-2xl font-bold text-white mb-4">Values</h3>
            <p className="text-gray-300 leading-relaxed">
              Innovation, teamwork, excellence, and never-stop-learning vibes! 💡 We share
              knowledge like memes, support each other through debugging nightmares, and
              push boundaries like there's no tomorrow! 🔥
            </p>
          </div>
        </div>

        {/* Interactive Journey Map */}
        <div className="mt-16">
          <h3 className="font-tech text-3xl font-bold text-white mb-8 text-center">
            Your <span className="bg-gradient-to-r from-robocell-yellow to-robocell-orange bg-clip-text text-transparent">Journey</span> Starts Here
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Level 1: Newbie */}
            <div className="relative group">
              <div className="card bg-gradient-to-br from-robocell-yellow/10 to-robocell-orange/10 border-robocell-yellow/30 hover:border-robocell-yellow transition-all duration-300">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-robocell-yellow to-robocell-orange flex items-center justify-center mr-4">
                    <Gamepad2 className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-tech text-lg font-bold text-white">Level 1: Newbie</h4>
                    <p className="text-robocell-yellow text-sm">Start your adventure!</p>
                  </div>
                </div>
                <ul className="text-gray-300 text-sm space-y-2">
                  <li>✅ Learn Arduino basics</li>
                  <li>✅ Build your first robot</li>
                  <li>✅ Join workshop sessions</li>
                  <li>✅ Meet fellow geeks</li>
                </ul>
                <div className="mt-4 bg-dark-800 rounded-full h-2">
                  <div className="bg-gradient-to-r from-robocell-yellow to-robocell-orange h-2 rounded-full w-full"></div>
                </div>
                <p className="text-xs text-robocell-yellow mt-2">🎯 Achievement Unlocked!</p>
              </div>
            </div>

            {/* Level 2: Builder */}
            <div className="relative group">
              <div className="card bg-gradient-to-br from-robocell-orange/10 to-electric-amber/10 border-robocell-orange/30 hover:border-robocell-orange transition-all duration-300">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-robocell-orange to-electric-amber flex items-center justify-center mr-4">
                    <Users className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-tech text-lg font-bold text-white">Level 2: Builder</h4>
                    <p className="text-robocell-orange text-sm">Level up your skills!</p>
                  </div>
                </div>
                <ul className="text-gray-300 text-sm space-y-2">
                  <li>🔄 Advanced programming</li>
                  <li>🔄 Team project leader</li>
                  <li>🔄 Competition participant</li>
                  <li>🔄 Mentor newcomers</li>
                </ul>
                <div className="mt-4 bg-dark-800 rounded-full h-2">
                  <div className="bg-gradient-to-r from-robocell-orange to-electric-amber h-2 rounded-full w-3/4"></div>
                </div>
                <p className="text-xs text-robocell-orange mt-2">🚀 In Progress...</p>
              </div>
            </div>

            {/* Level 3: Legend */}
            <div className="relative group">
              <div className="card bg-gradient-to-br from-electric-amber/10 to-robocell-yellow/10 border-electric-amber/30 hover:border-electric-amber transition-all duration-300">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-electric-amber to-robocell-yellow flex items-center justify-center mr-4">
                    <Zap className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-tech text-lg font-bold text-white">Level 3: Legend</h4>
                    <p className="text-electric-amber text-sm">Become the master!</p>
                  </div>
                </div>
                <ul className="text-gray-300 text-sm space-y-2">
                  <li>⭐ Robocon champion</li>
                  <li>⭐ Research publications</li>
                  <li>⭐ Industry internships</li>
                  <li>⭐ RoboCell alumni</li>
                </ul>
                <div className="mt-4 bg-dark-800 rounded-full h-2">
                  <div className="bg-gradient-to-r from-electric-amber to-robocell-yellow h-2 rounded-full w-1/4"></div>
                </div>
                <p className="text-xs text-electric-amber mt-2">🏆 Ultimate Goal!</p>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-12">
            <div className="inline-block bg-gradient-to-r from-robocell-yellow/20 to-robocell-orange/20 rounded-xl p-8 border border-robocell-yellow/30">
              <h4 className="font-tech text-2xl font-bold text-white mb-4">
                Ready to Start Your Quest? 🎮
              </h4>
              <p className="text-gray-300 mb-6 max-w-2xl">
                Join 587+ students who are already building the future. Your adventure in robotics starts with a single click!
              </p>
              <button className="bg-gradient-to-r from-robocell-yellow to-robocell-orange text-dark-900 px-8 py-4 rounded-lg font-bold text-lg hover:scale-105 transition-all duration-300 shadow-2xl hover:shadow-robocell-yellow/25">
                🚀 Join the Squad Now!
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Mission

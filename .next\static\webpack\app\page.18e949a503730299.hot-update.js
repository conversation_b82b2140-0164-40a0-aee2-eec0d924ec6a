"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/Hero.tsx":
/*!******************************************!*\
  !*** ./src/components/sections/Hero.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cpu_Gamepad2_Rocket_Target_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cpu,Gamepad2,Rocket,Target,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cpu_Gamepad2_Rocket_Target_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cpu,Gamepad2,Rocket,Target,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cpu_Gamepad2_Rocket_Target_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cpu,Gamepad2,Rocket,Target,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cpu_Gamepad2_Rocket_Target_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cpu,Gamepad2,Rocket,Target,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cpu_Gamepad2_Rocket_Target_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cpu,Gamepad2,Rocket,Target,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cpu_Gamepad2_Rocket_Target_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cpu,Gamepad2,Rocket,Target,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cpu_Gamepad2_Rocket_Target_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cpu,Gamepad2,Rocket,Target,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cpu_Gamepad2_Rocket_Target_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cpu,Gamepad2,Rocket,Target,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gamepad-2.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cpu_Gamepad2_Rocket_Target_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cpu,Gamepad2,Rocket,Target,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst HomeSection = ()=>{\n    _s();\n    const [currentText, setCurrentText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [level, setLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [clickStreak, setClickStreak] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [achievements, setAchievements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAchievement, setShowAchievement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gameElements, setGameElements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [powerUps, setPowerUps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [multiplier, setMultiplier] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(30);\n    const [gameActive, setGameActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [particles, setParticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const heroTexts = [\n        \"\\uD83C\\uDFAE Ready to Level Up Your Skills?\",\n        \"\\uD83D\\uDE80 Join the Ultimate Tech Adventure!\",\n        \"⚡ Unlock Your Robotics Potential!\",\n        \"\\uD83C\\uDFC6 Become a RoboCell Legend!\"\n    ];\n    const skillIcons = [\n        _barrel_optimize_names_Brain_Code_Cpu_Gamepad2_Rocket_Target_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        _barrel_optimize_names_Brain_Code_Cpu_Gamepad2_Rocket_Target_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        _barrel_optimize_names_Brain_Code_Cpu_Gamepad2_Rocket_Target_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        _barrel_optimize_names_Brain_Code_Cpu_Gamepad2_Rocket_Target_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _barrel_optimize_names_Brain_Code_Cpu_Gamepad2_Rocket_Target_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        _barrel_optimize_names_Brain_Code_Cpu_Gamepad2_Rocket_Target_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    ];\n    // Game mechanics\n    const startGame = ()=>{\n        setGameActive(true);\n        setTimeLeft(30);\n        setScore(0);\n        setClickStreak(0);\n        setMultiplier(1);\n        generateGameElements();\n    };\n    const generateGameElements = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!gameActive) return;\n        const newElement = {\n            id: Date.now(),\n            x: Math.random() * 80 + 10,\n            y: Math.random() * 60 + 20,\n            type: skillIcons[Math.floor(Math.random() * skillIcons.length)].name,\n            points: Math.floor(Math.random() * 50) + 10\n        };\n        setGameElements((prev)=>[\n                ...prev,\n                newElement\n            ]);\n        // Remove element after 3 seconds if not clicked\n        setTimeout(()=>{\n            setGameElements((prev)=>prev.filter((el)=>el.id !== newElement.id));\n        }, 3000);\n    }, [\n        gameActive\n    ]);\n    const handleElementClick = (element)=>{\n        setScore((prev)=>prev + element.points * multiplier);\n        setClickStreak((prev)=>prev + 1);\n        setGameElements((prev)=>prev.filter((el)=>el.id !== element.id));\n        // Create particle effect\n        const newParticles = Array.from({\n            length: 5\n        }, (_, i)=>({\n                id: Date.now() + i,\n                x: element.x,\n                y: element.y,\n                vx: (Math.random() - 0.5) * 10,\n                vy: (Math.random() - 0.5) * 10\n            }));\n        setParticles((prev)=>[\n                ...prev,\n                ...newParticles\n            ]);\n        // Check for achievements\n        checkAchievements();\n        // Increase multiplier on streak\n        if (clickStreak > 0 && clickStreak % 5 === 0) {\n            setMultiplier((prev)=>Math.min(prev + 0.5, 5));\n        }\n    };\n    const checkAchievements = ()=>{\n        const newAchievements = [];\n        if (score >= 100 && !achievements.includes(\"First Century\")) {\n            newAchievements.push(\"First Century\");\n        }\n        if (clickStreak >= 10 && !achievements.includes(\"Streak Master\")) {\n            newAchievements.push(\"Streak Master\");\n        }\n        if (score >= 500 && !achievements.includes(\"Robot Overlord\")) {\n            newAchievements.push(\"Robot Overlord\");\n        }\n        if (newAchievements.length > 0) {\n            setAchievements((prev)=>[\n                    ...prev,\n                    ...newAchievements\n                ]);\n            setShowAchievement(newAchievements[0]);\n            setTimeout(()=>setShowAchievement(null), 3000);\n        }\n    };\n    // Game timer\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (gameActive && timeLeft > 0) {\n            const timer = setTimeout(()=>setTimeLeft((prev)=>prev - 1), 1000);\n            return ()=>clearTimeout(timer);\n        } else if (timeLeft === 0) {\n            setGameActive(false);\n            setGameElements([]);\n        }\n    }, [\n        gameActive,\n        timeLeft\n    ]);\n    // Generate game elements\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (gameActive) {\n            const interval = setInterval(generateGameElements, 1500);\n            return ()=>clearInterval(interval);\n        }\n    }, [\n        gameActive,\n        generateGameElements\n    ]);\n    // Text rotation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            setCurrentText((prev)=>(prev + 1) % heroTexts.length);\n        }, 3000);\n        return ()=>clearInterval(interval);\n    }, []);\n    // Particle animation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            setParticles((prev)=>prev.filter((p)=>Date.now() - p.id < 1000));\n        }, 100);\n        return ()=>clearInterval(interval);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-dark-800 via-dark-900 to-dark-950 pt-32\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-circuit-pattern opacity-10\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-robocell-yellow/5 via-transparent to-robocell-orange/5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, undefined),\n            gameActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-20 left-4 right-4 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center bg-black/50 backdrop-blur-md rounded-xl p-4 border border-robocell-yellow/30\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-robocell-yellow font-tech text-sm\",\n                                            children: \"SCORE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white font-bold text-xl\",\n                                            children: score.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-robocell-orange font-tech text-sm\",\n                                            children: \"STREAK\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white font-bold text-xl\",\n                                            children: clickStreak\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-electric-amber font-tech text-sm\",\n                                            children: \"MULTIPLIER\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white font-bold text-xl\",\n                                            children: [\n                                                multiplier,\n                                                \"x\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-400 font-tech text-sm\",\n                                    children: \"TIME\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white font-bold text-2xl\",\n                                    children: [\n                                        timeLeft,\n                                        \"s\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, undefined),\n            showAchievement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-32 left-1/2 transform -translate-x-1/2 z-30 animate-bounce\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-robocell-yellow to-robocell-orange rounded-xl p-4 border-2 border-white shadow-2xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Code_Cpu_Gamepad2_Rocket_Target_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-6 w-6 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white font-tech font-bold\",\n                                children: [\n                                    \"Achievement Unlocked: \",\n                                    showAchievement,\n                                    \"!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                lineNumber: 172,\n                columnNumber: 9\n            }, undefined),\n            gameElements.map((element)=>{\n                const IconComponent = skillIcons.find((icon)=>icon.name === element.type) || _barrel_optimize_names_Brain_Code_Cpu_Gamepad2_Rocket_Target_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute z-10 cursor-pointer transform hover:scale-110 transition-all duration-200\",\n                    style: {\n                        left: \"\".concat(element.x, \"%\"),\n                        top: \"\".concat(element.y, \"%\")\n                    },\n                    onClick: ()=>handleElementClick(element),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 rounded-full bg-gradient-to-br from-robocell-yellow to-robocell-orange flex items-center justify-center animate-pulse hover:animate-bounce shadow-lg shadow-robocell-yellow/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                    className: \"h-8 w-8 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black/80 text-robocell-yellow text-xs px-2 py-1 rounded font-tech opacity-0 group-hover:opacity-100 transition-opacity\",\n                                children: [\n                                    \"+\",\n                                    element.points\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 w-16 h-16 rounded-full bg-robocell-yellow/30 animate-ping\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 13\n                    }, undefined)\n                }, element.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 11\n                }, undefined);\n            }),\n            particles.map((particle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute w-2 h-2 bg-robocell-yellow rounded-full animate-ping z-10\",\n                    style: {\n                        left: \"\".concat(particle.x, \"%\"),\n                        top: \"\".concat(particle.y, \"%\"),\n                        transform: \"translate(\".concat(particle.vx, \"px, \").concat(particle.vy, \"px)\")\n                    }\n                }, particle.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, undefined)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"font-tech text-4xl md:text-6xl lg:text-7xl font-bold text-white leading-tight\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block bg-gradient-to-r from-robocell-yellow via-robocell-orange to-electric-amber bg-clip-text text-transparent\",\n                                            children: \"RoboCell\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-2xl md:text-3xl lg:text-4xl font-normal text-gray-300 mt-2\",\n                                            children: \"NIT Durgapur\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-16 md:h-20 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-tech text-xl md:text-3xl lg:text-4xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-electric-yellow via-robocell-orange to-electric-amber animate-pulse\",\n                                        children: heroTexts[currentText]\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto\",\n                            children: !gameActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg md:text-xl text-gray-300 leading-relaxed\",\n                                        children: \"\\uD83C\\uDFAE Welcome to RoboCell's Interactive Challenge! Test your reflexes and unlock achievements while learning about our amazing robotics journey. Click the floating tech icons to score points and discover what makes us the ultimate robotics club! \\uD83D\\uDE80\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mt-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-robocell-yellow/20 to-robocell-orange/20 rounded-xl p-4 border border-robocell-yellow/30 backdrop-blur-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl mb-2\",\n                                                        children: \"\\uD83C\\uDFC6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-robocell-yellow font-tech text-sm\",\n                                                        children: \"ACHIEVEMENTS\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-bold text-xl\",\n                                                        children: [\n                                                            achievements.length,\n                                                            \"/10\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-robocell-orange/20 to-electric-amber/20 rounded-xl p-4 border border-robocell-orange/30 backdrop-blur-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl mb-2\",\n                                                        children: \"⚡\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-robocell-orange font-tech text-sm\",\n                                                        children: \"HIGH SCORE\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-bold text-xl\",\n                                                        children: Math.max(score, 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-electric-amber/20 to-robocell-yellow/20 rounded-xl p-4 border border-electric-amber/30 backdrop-blur-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl mb-2\",\n                                                        children: \"\\uD83C\\uDFAF\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-electric-amber font-tech text-sm\",\n                                                        children: \"BEST STREAK\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-bold text-xl\",\n                                                        children: Math.max(clickStreak, 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-br from-neon-blue/20 to-robocell-yellow/20 rounded-xl p-4 border border-neon-blue/30 backdrop-blur-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl mb-2\",\n                                                        children: \"\\uD83D\\uDE80\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-neon-blue font-tech text-sm\",\n                                                        children: \"LEVEL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-bold text-xl\",\n                                                        children: level\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-robocell-yellow font-tech animate-pulse\",\n                                        children: \"\\uD83C\\uDFAF Click the floating icons to score points! \\uD83C\\uDFAF\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-300 mt-2\",\n                                        children: \"Build streaks for multipliers • Unlock achievements • Become a RoboCell legend!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-12 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center items-center space-x-8\",\n                                    children: !gameActive ? /* Start Game Button */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative group cursor-pointer\",\n                                        onClick: startGame,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-32 h-32 md:w-40 md:h-40 rounded-full border-4 border-robocell-yellow bg-gradient-to-br from-robocell-yellow via-robocell-orange to-electric-amber flex items-center justify-center group-hover:scale-110 transition-all duration-500 shadow-2xl animate-pulse hover:animate-bounce\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Code_Cpu_Gamepad2_Rocket_Target_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-12 w-12 md:h-16 md:w-16 text-white mx-auto mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-white font-tech font-bold text-sm md:text-base\",\n                                                            children: \"START GAME\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 w-32 h-32 md:w-40 md:h-40 rounded-full bg-gradient-to-br from-robocell-yellow to-robocell-orange opacity-0 group-hover:opacity-30 group-hover:animate-ping\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-12 left-1/2 transform -translate-x-1/2 text-robocell-yellow text-sm font-tech opacity-0 group-hover:opacity-100 transition-opacity text-center\",\n                                                children: \"\\uD83C\\uDFAE Click to start the challenge!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 17\n                                    }, undefined) : /* Game Active Display */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-24 h-24 md:w-32 md:h-32 rounded-full border-4 border-green-400 bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center animate-pulse shadow-2xl\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Code_Cpu_Gamepad2_Rocket_Target_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-12 w-12 md:h-16 md:w-16 text-white animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 text-green-400 font-tech font-bold animate-bounce\",\n                                                children: \"GAME ACTIVE!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, undefined),\n                                achievements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-robocell-yellow font-tech text-lg mb-4\",\n                                            children: \"\\uD83C\\uDFC6 Your Achievements\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap justify-center gap-2\",\n                                            children: achievements.map((achievement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-robocell-yellow/20 to-robocell-orange/20 backdrop-blur-sm rounded-full px-4 py-2 border border-robocell-yellow/50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-robocell-yellow font-tech text-sm\",\n                                                        children: achievement\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, index, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-0 left-0 w-full h-full pointer-events-none\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 left-1/4 animate-float bg-robocell-yellow/20 backdrop-blur-sm rounded-full px-3 py-1 text-robocell-yellow text-xs font-bold\",\n                                            children: \"\\uD83C\\uDFC6 Robocon Champions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-8 right-1/4 animate-float bg-robocell-orange/20 backdrop-blur-sm rounded-full px-3 py-1 text-robocell-orange text-xs font-bold\",\n                                            style: {\n                                                animationDelay: \"1s\"\n                                            },\n                                            children: \"⚡ 587+ Active Members\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-4 left-1/3 animate-float bg-electric-amber/20 backdrop-blur-sm rounded-full px-3 py-1 text-electric-amber text-xs font-bold\",\n                                            style: {\n                                                animationDelay: \"2s\"\n                                            },\n                                            children: \"\\uD83D\\uDE80 30+ Live Projects\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-8 right-1/3 animate-float bg-neon-blue/20 backdrop-blur-sm rounded-full px-3 py-1 text-neon-blue text-xs font-bold\",\n                                            style: {\n                                                animationDelay: \"3s\"\n                                            },\n                                            children: \"\\uD83C\\uDFAF Join the Adventure!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-6 h-10 border-2 border-robocell-yellow rounded-full flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-1 h-3 bg-gradient-to-b from-robocell-yellow to-robocell-orange rounded-full mt-2 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                lineNumber: 349,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n        lineNumber: 139,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HomeSection, \"0THI3MLw8BtGrd6jOuiHUCEYQMM=\");\n_c = HomeSection;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomeSection);\nvar _c;\n$RefreshReg$(_c, \"HomeSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3NlY3Rpb25zL0hlcm8udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUV3RDtBQUU2RTtBQUVySSxNQUFNWSxjQUFjOztJQUNsQixNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBR2QsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDZSxPQUFPQyxTQUFTLEdBQUdoQiwrQ0FBUUEsQ0FBQztJQUNuQyxNQUFNLENBQUNpQixPQUFPQyxTQUFTLEdBQUdsQiwrQ0FBUUEsQ0FBQztJQUNuQyxNQUFNLENBQUNtQixhQUFhQyxlQUFlLEdBQUdwQiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNxQixjQUFjQyxnQkFBZ0IsR0FBR3RCLCtDQUFRQSxDQUFXLEVBQUU7SUFDN0QsTUFBTSxDQUFDdUIsaUJBQWlCQyxtQkFBbUIsR0FBR3hCLCtDQUFRQSxDQUFnQjtJQUN0RSxNQUFNLENBQUN5QixjQUFjQyxnQkFBZ0IsR0FBRzFCLCtDQUFRQSxDQUEwRSxFQUFFO0lBQzVILE1BQU0sQ0FBQzJCLFVBQVVDLFlBQVksR0FBRzVCLCtDQUFRQSxDQUEwRCxFQUFFO0lBQ3BHLE1BQU0sQ0FBQzZCLFlBQVlDLGNBQWMsR0FBRzlCLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQytCLFVBQVVDLFlBQVksR0FBR2hDLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ2lDLFlBQVlDLGNBQWMsR0FBR2xDLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ21DLFdBQVdDLGFBQWEsR0FBR3BDLCtDQUFRQSxDQUFvRSxFQUFFO0lBRWhILE1BQU1xQyxZQUFZO1FBQ2hCO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7SUFFRCxNQUFNQyxhQUFhO1FBQUNuQyxtSUFBR0E7UUFBRUMsbUlBQUdBO1FBQUVNLG1JQUFJQTtRQUFFQyxtSUFBTUE7UUFBRUYsbUlBQUtBO1FBQUVELG1JQUFNQTtLQUFDO0lBRTFELGlCQUFpQjtJQUNqQixNQUFNK0IsWUFBWTtRQUNoQkwsY0FBYztRQUNkRixZQUFZO1FBQ1poQixTQUFTO1FBQ1RJLGVBQWU7UUFDZlUsY0FBYztRQUNkVTtJQUNGO0lBRUEsTUFBTUEsdUJBQXVCdEMsa0RBQVdBLENBQUM7UUFDdkMsSUFBSSxDQUFDK0IsWUFBWTtRQUVqQixNQUFNUSxhQUFhO1lBQ2pCQyxJQUFJQyxLQUFLQyxHQUFHO1lBQ1pDLEdBQUdDLEtBQUtDLE1BQU0sS0FBSyxLQUFLO1lBQ3hCQyxHQUFHRixLQUFLQyxNQUFNLEtBQUssS0FBSztZQUN4QkUsTUFBTVgsVUFBVSxDQUFDUSxLQUFLSSxLQUFLLENBQUNKLEtBQUtDLE1BQU0sS0FBS1QsV0FBV2EsTUFBTSxFQUFFLENBQUNDLElBQUk7WUFDcEVDLFFBQVFQLEtBQUtJLEtBQUssQ0FBQ0osS0FBS0MsTUFBTSxLQUFLLE1BQU07UUFDM0M7UUFFQXJCLGdCQUFnQjRCLENBQUFBLE9BQVE7bUJBQUlBO2dCQUFNYjthQUFXO1FBRTdDLGdEQUFnRDtRQUNoRGMsV0FBVztZQUNUN0IsZ0JBQWdCNEIsQ0FBQUEsT0FBUUEsS0FBS0UsTUFBTSxDQUFDQyxDQUFBQSxLQUFNQSxHQUFHZixFQUFFLEtBQUtELFdBQVdDLEVBQUU7UUFDbkUsR0FBRztJQUNMLEdBQUc7UUFBQ1Q7S0FBVztJQUVmLE1BQU15QixxQkFBcUIsQ0FBQ0M7UUFDMUIzQyxTQUFTc0MsQ0FBQUEsT0FBUUEsT0FBUUssUUFBUU4sTUFBTSxHQUFHeEI7UUFDMUNULGVBQWVrQyxDQUFBQSxPQUFRQSxPQUFPO1FBQzlCNUIsZ0JBQWdCNEIsQ0FBQUEsT0FBUUEsS0FBS0UsTUFBTSxDQUFDQyxDQUFBQSxLQUFNQSxHQUFHZixFQUFFLEtBQUtpQixRQUFRakIsRUFBRTtRQUU5RCx5QkFBeUI7UUFDekIsTUFBTWtCLGVBQWVDLE1BQU1DLElBQUksQ0FBQztZQUFDWCxRQUFRO1FBQUMsR0FBRyxDQUFDWSxHQUFHQyxJQUFPO2dCQUN0RHRCLElBQUlDLEtBQUtDLEdBQUcsS0FBS29CO2dCQUNqQm5CLEdBQUdjLFFBQVFkLENBQUM7Z0JBQ1pHLEdBQUdXLFFBQVFYLENBQUM7Z0JBQ1ppQixJQUFJLENBQUNuQixLQUFLQyxNQUFNLEtBQUssR0FBRSxJQUFLO2dCQUM1Qm1CLElBQUksQ0FBQ3BCLEtBQUtDLE1BQU0sS0FBSyxHQUFFLElBQUs7WUFDOUI7UUFDQVgsYUFBYWtCLENBQUFBLE9BQVE7bUJBQUlBO21CQUFTTTthQUFhO1FBRS9DLHlCQUF5QjtRQUN6Qk87UUFFQSxnQ0FBZ0M7UUFDaEMsSUFBSWhELGNBQWMsS0FBS0EsY0FBYyxNQUFNLEdBQUc7WUFDNUNXLGNBQWN3QixDQUFBQSxPQUFRUixLQUFLc0IsR0FBRyxDQUFDZCxPQUFPLEtBQUs7UUFDN0M7SUFDRjtJQUVBLE1BQU1hLG9CQUFvQjtRQUN4QixNQUFNRSxrQkFBa0IsRUFBRTtRQUUxQixJQUFJdEQsU0FBUyxPQUFPLENBQUNNLGFBQWFpRCxRQUFRLENBQUMsa0JBQWtCO1lBQzNERCxnQkFBZ0JFLElBQUksQ0FBQztRQUN2QjtRQUNBLElBQUlwRCxlQUFlLE1BQU0sQ0FBQ0UsYUFBYWlELFFBQVEsQ0FBQyxrQkFBa0I7WUFDaEVELGdCQUFnQkUsSUFBSSxDQUFDO1FBQ3ZCO1FBQ0EsSUFBSXhELFNBQVMsT0FBTyxDQUFDTSxhQUFhaUQsUUFBUSxDQUFDLG1CQUFtQjtZQUM1REQsZ0JBQWdCRSxJQUFJLENBQUM7UUFDdkI7UUFFQSxJQUFJRixnQkFBZ0JsQixNQUFNLEdBQUcsR0FBRztZQUM5QjdCLGdCQUFnQmdDLENBQUFBLE9BQVE7dUJBQUlBO3VCQUFTZTtpQkFBZ0I7WUFDckQ3QyxtQkFBbUI2QyxlQUFlLENBQUMsRUFBRTtZQUNyQ2QsV0FBVyxJQUFNL0IsbUJBQW1CLE9BQU87UUFDN0M7SUFDRjtJQUVBLGFBQWE7SUFDYnZCLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSWdDLGNBQWNGLFdBQVcsR0FBRztZQUM5QixNQUFNeUMsUUFBUWpCLFdBQVcsSUFBTXZCLFlBQVlzQixDQUFBQSxPQUFRQSxPQUFPLElBQUk7WUFDOUQsT0FBTyxJQUFNbUIsYUFBYUQ7UUFDNUIsT0FBTyxJQUFJekMsYUFBYSxHQUFHO1lBQ3pCRyxjQUFjO1lBQ2RSLGdCQUFnQixFQUFFO1FBQ3BCO0lBQ0YsR0FBRztRQUFDTztRQUFZRjtLQUFTO0lBRXpCLHlCQUF5QjtJQUN6QjlCLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSWdDLFlBQVk7WUFDZCxNQUFNeUMsV0FBV0MsWUFBWW5DLHNCQUFzQjtZQUNuRCxPQUFPLElBQU1vQyxjQUFjRjtRQUM3QjtJQUNGLEdBQUc7UUFBQ3pDO1FBQVlPO0tBQXFCO0lBRXJDLGdCQUFnQjtJQUNoQnZDLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTXlFLFdBQVdDLFlBQVk7WUFDM0I3RCxlQUFlLENBQUN3QyxPQUFTLENBQUNBLE9BQU8sS0FBS2pCLFVBQVVjLE1BQU07UUFDeEQsR0FBRztRQUNILE9BQU8sSUFBTXlCLGNBQWNGO0lBQzdCLEdBQUcsRUFBRTtJQUVMLHFCQUFxQjtJQUNyQnpFLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTXlFLFdBQVdDLFlBQVk7WUFDM0J2QyxhQUFha0IsQ0FBQUEsT0FBUUEsS0FBS0UsTUFBTSxDQUFDcUIsQ0FBQUEsSUFBS2xDLEtBQUtDLEdBQUcsS0FBS2lDLEVBQUVuQyxFQUFFLEdBQUc7UUFDNUQsR0FBRztRQUNILE9BQU8sSUFBTWtDLGNBQWNGO0lBQzdCLEdBQUcsRUFBRTtJQUVMLHFCQUNFLDhEQUFDSTtRQUFRQyxXQUFVOzswQkFFakIsOERBQUNDO2dCQUFJRCxXQUFVOzs7Ozs7MEJBQ2YsOERBQUNDO2dCQUFJRCxXQUFVOzs7Ozs7WUFHZDlDLDRCQUNDLDhEQUFDK0M7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNDO29CQUFJRCxXQUFVOztzQ0FDYiw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNDOzRDQUFJRCxXQUFVO3NEQUF5Qzs7Ozs7O3NEQUN4RCw4REFBQ0M7NENBQUlELFdBQVU7c0RBQWdDaEUsTUFBTWtFLGNBQWM7Ozs7Ozs7Ozs7Ozs4Q0FFckUsOERBQUNEO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ0M7NENBQUlELFdBQVU7c0RBQXlDOzs7Ozs7c0RBQ3hELDhEQUFDQzs0Q0FBSUQsV0FBVTtzREFBZ0M1RDs7Ozs7Ozs7Ozs7OzhDQUVqRCw4REFBQzZEO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ0M7NENBQUlELFdBQVU7c0RBQXdDOzs7Ozs7c0RBQ3ZELDhEQUFDQzs0Q0FBSUQsV0FBVTs7Z0RBQWdDbEQ7Z0RBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBRzlELDhEQUFDbUQ7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDQztvQ0FBSUQsV0FBVTs4Q0FBaUM7Ozs7Ozs4Q0FDaEQsOERBQUNDO29DQUFJRCxXQUFVOzt3Q0FBaUNoRDt3Q0FBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBT2hFUixpQ0FDQyw4REFBQ3lEO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDQztvQkFBSUQsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDMUUsbUlBQU1BO2dDQUFDMEUsV0FBVTs7Ozs7OzBDQUNsQiw4REFBQ0c7Z0NBQUtILFdBQVU7O29DQUFpQztvQ0FBdUJ4RDtvQ0FBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBTy9GRSxhQUFhMEQsR0FBRyxDQUFDLENBQUN4QjtnQkFDakIsTUFBTXlCLGdCQUFnQjlDLFdBQVcrQyxJQUFJLENBQUNDLENBQUFBLE9BQVFBLEtBQUtsQyxJQUFJLEtBQUtPLFFBQVFWLElBQUksS0FBSzlDLG1JQUFHQTtnQkFDaEYscUJBQ0UsOERBQUM2RTtvQkFFQ0QsV0FBVTtvQkFDVlEsT0FBTzt3QkFBRUMsTUFBTSxHQUFhLE9BQVY3QixRQUFRZCxDQUFDLEVBQUM7d0JBQUk0QyxLQUFLLEdBQWEsT0FBVjlCLFFBQVFYLENBQUMsRUFBQztvQkFBRztvQkFDckQwQyxTQUFTLElBQU1oQyxtQkFBbUJDOzhCQUVsQyw0RUFBQ3FCO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ0M7Z0NBQUlELFdBQVU7MENBQ2IsNEVBQUNLO29DQUFjTCxXQUFVOzs7Ozs7Ozs7OzswQ0FFM0IsOERBQUNDO2dDQUFJRCxXQUFVOztvQ0FBZ0w7b0NBQzNMcEIsUUFBUU4sTUFBTTs7Ozs7OzswQ0FFbEIsOERBQUMyQjtnQ0FBSUQsV0FBVTs7Ozs7Ozs7Ozs7O21CQVpacEIsUUFBUWpCLEVBQUU7Ozs7O1lBZ0JyQjtZQUdDUCxVQUFVZ0QsR0FBRyxDQUFDLENBQUNRLHlCQUNkLDhEQUFDWDtvQkFFQ0QsV0FBVTtvQkFDVlEsT0FBTzt3QkFDTEMsTUFBTSxHQUFjLE9BQVhHLFNBQVM5QyxDQUFDLEVBQUM7d0JBQ3BCNEMsS0FBSyxHQUFjLE9BQVhFLFNBQVMzQyxDQUFDLEVBQUM7d0JBQ25CNEMsV0FBVyxhQUErQkQsT0FBbEJBLFNBQVMxQixFQUFFLEVBQUMsUUFBa0IsT0FBWjBCLFNBQVN6QixFQUFFLEVBQUM7b0JBQ3hEO21CQU5LeUIsU0FBU2pELEVBQUU7Ozs7OzBCQVVwQiw4REFBQ3NDO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDQztvQkFBSUQsV0FBVTs7c0NBRWIsOERBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ2M7b0NBQUdkLFdBQVU7O3NEQUNaLDhEQUFDRzs0Q0FBS0gsV0FBVTtzREFBa0g7Ozs7OztzREFHbEksOERBQUNHOzRDQUFLSCxXQUFVO3NEQUF3RTs7Ozs7Ozs7Ozs7OzhDQU0xRiw4REFBQ0M7b0NBQUlELFdBQVU7OENBQ2IsNEVBQUNlO3dDQUFHZixXQUFVO2tEQUNYMUMsU0FBUyxDQUFDeEIsWUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTTdCLDhEQUFDbUU7NEJBQUlELFdBQVU7c0NBQ1osQ0FBQzlDLDJCQUNBLDhEQUFDK0M7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDRjt3Q0FBRUUsV0FBVTtrREFBbUQ7Ozs7OztrREFNaEUsOERBQUNDO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ0M7Z0RBQUlELFdBQVU7O2tFQUNiLDhEQUFDQzt3REFBSUQsV0FBVTtrRUFBZ0I7Ozs7OztrRUFDL0IsOERBQUNDO3dEQUFJRCxXQUFVO2tFQUF5Qzs7Ozs7O2tFQUN4RCw4REFBQ0M7d0RBQUlELFdBQVU7OzREQUFnQzFELGFBQWE4QixNQUFNOzREQUFDOzs7Ozs7Ozs7Ozs7OzBEQUVyRSw4REFBQzZCO2dEQUFJRCxXQUFVOztrRUFDYiw4REFBQ0M7d0RBQUlELFdBQVU7a0VBQWdCOzs7Ozs7a0VBQy9CLDhEQUFDQzt3REFBSUQsV0FBVTtrRUFBeUM7Ozs7OztrRUFDeEQsOERBQUNDO3dEQUFJRCxXQUFVO2tFQUFnQ2pDLEtBQUtpRCxHQUFHLENBQUNoRixPQUFPOzs7Ozs7Ozs7Ozs7MERBRWpFLDhEQUFDaUU7Z0RBQUlELFdBQVU7O2tFQUNiLDhEQUFDQzt3REFBSUQsV0FBVTtrRUFBZ0I7Ozs7OztrRUFDL0IsOERBQUNDO3dEQUFJRCxXQUFVO2tFQUF3Qzs7Ozs7O2tFQUN2RCw4REFBQ0M7d0RBQUlELFdBQVU7a0VBQWdDakMsS0FBS2lELEdBQUcsQ0FBQzVFLGFBQWE7Ozs7Ozs7Ozs7OzswREFFdkUsOERBQUM2RDtnREFBSUQsV0FBVTs7a0VBQ2IsOERBQUNDO3dEQUFJRCxXQUFVO2tFQUFnQjs7Ozs7O2tFQUMvQiw4REFBQ0M7d0RBQUlELFdBQVU7a0VBQW1DOzs7Ozs7a0VBQ2xELDhEQUFDQzt3REFBSUQsV0FBVTtrRUFBZ0M5RDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBS3JELDhEQUFDK0Q7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDRjt3Q0FBRUUsV0FBVTtrREFBdUQ7Ozs7OztrREFHcEUsOERBQUNGO3dDQUFFRSxXQUFVO2tEQUFxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBUXhDLDhEQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNDO29DQUFJRCxXQUFVOzhDQUNaLENBQUM5QyxhQUNBLHFCQUFxQixpQkFDckIsOERBQUMrQzt3Q0FBSUQsV0FBVTt3Q0FBZ0NXLFNBQVNuRDs7MERBQ3RELDhEQUFDeUM7Z0RBQUlELFdBQVU7MERBQ2IsNEVBQUNDO29EQUFJRCxXQUFVOztzRUFDYiw4REFBQ3hFLG1JQUFRQTs0REFBQ3dFLFdBQVU7Ozs7OztzRUFDcEIsOERBQUNDOzREQUFJRCxXQUFVO3NFQUFzRDs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBR3pFLDhEQUFDQztnREFBSUQsV0FBVTs7Ozs7OzBEQUVmLDhEQUFDQztnREFBSUQsV0FBVTswREFBa0s7Ozs7Ozs7Ozs7O29EQUtuTCx1QkFBdUIsaUJBQ3ZCLDhEQUFDQzt3Q0FBSUQsV0FBVTs7MERBQ2IsOERBQUNDO2dEQUFJRCxXQUFVOzBEQUNiLDRFQUFDekUsb0lBQU1BO29EQUFDeUUsV0FBVTs7Ozs7Ozs7Ozs7MERBRXBCLDhEQUFDQztnREFBSUQsV0FBVTswREFBeUQ7Ozs7Ozs7Ozs7Ozs7Ozs7O2dDQVE3RTFELGFBQWE4QixNQUFNLEdBQUcsbUJBQ3JCLDhEQUFDNkI7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDaUI7NENBQUdqQixXQUFVO3NEQUE4Qzs7Ozs7O3NEQUM1RCw4REFBQ0M7NENBQUlELFdBQVU7c0RBQ1oxRCxhQUFhOEQsR0FBRyxDQUFDLENBQUNjLGFBQWFDLHNCQUM5Qiw4REFBQ2xCO29EQUFnQkQsV0FBVTs4REFDekIsNEVBQUNHO3dEQUFLSCxXQUFVO2tFQUEwQ2tCOzs7Ozs7bURBRGxEQzs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FTbEIsOERBQUNsQjtvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNDOzRDQUFJRCxXQUFVO3NEQUE2STs7Ozs7O3NEQUc1Siw4REFBQ0M7NENBQUlELFdBQVU7NENBQThJUSxPQUFPO2dEQUFFWSxnQkFBZ0I7NENBQUs7c0RBQUc7Ozs7OztzREFHOUwsOERBQUNuQjs0Q0FBSUQsV0FBVTs0Q0FBOElRLE9BQU87Z0RBQUVZLGdCQUFnQjs0Q0FBSztzREFBRzs7Ozs7O3NEQUc5TCw4REFBQ25COzRDQUFJRCxXQUFVOzRDQUFxSVEsT0FBTztnREFBRVksZ0JBQWdCOzRDQUFLO3NEQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTN0wsOERBQUNuQjtnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ0M7b0JBQUlELFdBQVU7OEJBQ2IsNEVBQUNDO3dCQUFJRCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS3pCO0dBN1ZNbkU7S0FBQUE7QUErVk4sK0RBQWVBLFdBQVdBLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvc2VjdGlvbnMvSGVyby50c3g/MGUxNCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCdcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluaydcbmltcG9ydCB7IEFycm93UmlnaHQsIFBsYXksIENwdSwgWmFwLCBDb2csIFNwYXJrbGVzLCBUcm9waHksIFRhcmdldCwgR2FtZXBhZDIsIFN0YXIsIFJvY2tldCwgQnJhaW4sIENvZGUsIFdyZW5jaCB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcblxuY29uc3QgSG9tZVNlY3Rpb24gPSAoKSA9PiB7XG4gIGNvbnN0IFtjdXJyZW50VGV4dCwgc2V0Q3VycmVudFRleHRdID0gdXNlU3RhdGUoMClcbiAgY29uc3QgW3Njb3JlLCBzZXRTY29yZV0gPSB1c2VTdGF0ZSgwKVxuICBjb25zdCBbbGV2ZWwsIHNldExldmVsXSA9IHVzZVN0YXRlKDEpXG4gIGNvbnN0IFtjbGlja1N0cmVhaywgc2V0Q2xpY2tTdHJlYWtdID0gdXNlU3RhdGUoMClcbiAgY29uc3QgW2FjaGlldmVtZW50cywgc2V0QWNoaWV2ZW1lbnRzXSA9IHVzZVN0YXRlPHN0cmluZ1tdPihbXSlcbiAgY29uc3QgW3Nob3dBY2hpZXZlbWVudCwgc2V0U2hvd0FjaGlldmVtZW50XSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtnYW1lRWxlbWVudHMsIHNldEdhbWVFbGVtZW50c10gPSB1c2VTdGF0ZTxBcnJheTx7aWQ6IG51bWJlciwgeDogbnVtYmVyLCB5OiBudW1iZXIsIHR5cGU6IHN0cmluZywgcG9pbnRzOiBudW1iZXJ9Pj4oW10pXG4gIGNvbnN0IFtwb3dlclVwcywgc2V0UG93ZXJVcHNdID0gdXNlU3RhdGU8QXJyYXk8e2lkOiBudW1iZXIsIHg6IG51bWJlciwgeTogbnVtYmVyLCB0eXBlOiBzdHJpbmd9Pj4oW10pXG4gIGNvbnN0IFttdWx0aXBsaWVyLCBzZXRNdWx0aXBsaWVyXSA9IHVzZVN0YXRlKDEpXG4gIGNvbnN0IFt0aW1lTGVmdCwgc2V0VGltZUxlZnRdID0gdXNlU3RhdGUoMzApXG4gIGNvbnN0IFtnYW1lQWN0aXZlLCBzZXRHYW1lQWN0aXZlXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbcGFydGljbGVzLCBzZXRQYXJ0aWNsZXNdID0gdXNlU3RhdGU8QXJyYXk8e2lkOiBudW1iZXIsIHg6IG51bWJlciwgeTogbnVtYmVyLCB2eDogbnVtYmVyLCB2eTogbnVtYmVyfT4+KFtdKVxuXG4gIGNvbnN0IGhlcm9UZXh0cyA9IFtcbiAgICBcIvCfjq4gUmVhZHkgdG8gTGV2ZWwgVXAgWW91ciBTa2lsbHM/XCIsXG4gICAgXCLwn5qAIEpvaW4gdGhlIFVsdGltYXRlIFRlY2ggQWR2ZW50dXJlIVwiLFxuICAgIFwi4pqhIFVubG9jayBZb3VyIFJvYm90aWNzIFBvdGVudGlhbCFcIixcbiAgICBcIvCfj4YgQmVjb21lIGEgUm9ib0NlbGwgTGVnZW5kIVwiXG4gIF1cblxuICBjb25zdCBza2lsbEljb25zID0gW0NwdSwgWmFwLCBDb2RlLCBXcmVuY2gsIEJyYWluLCBSb2NrZXRdXG5cbiAgLy8gR2FtZSBtZWNoYW5pY3NcbiAgY29uc3Qgc3RhcnRHYW1lID0gKCkgPT4ge1xuICAgIHNldEdhbWVBY3RpdmUodHJ1ZSlcbiAgICBzZXRUaW1lTGVmdCgzMClcbiAgICBzZXRTY29yZSgwKVxuICAgIHNldENsaWNrU3RyZWFrKDApXG4gICAgc2V0TXVsdGlwbGllcigxKVxuICAgIGdlbmVyYXRlR2FtZUVsZW1lbnRzKClcbiAgfVxuXG4gIGNvbnN0IGdlbmVyYXRlR2FtZUVsZW1lbnRzID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlmICghZ2FtZUFjdGl2ZSkgcmV0dXJuXG5cbiAgICBjb25zdCBuZXdFbGVtZW50ID0ge1xuICAgICAgaWQ6IERhdGUubm93KCksXG4gICAgICB4OiBNYXRoLnJhbmRvbSgpICogODAgKyAxMCwgLy8gMTAlIHRvIDkwJSBvZiBzY3JlZW4gd2lkdGhcbiAgICAgIHk6IE1hdGgucmFuZG9tKCkgKiA2MCArIDIwLCAvLyAyMCUgdG8gODAlIG9mIHNjcmVlbiBoZWlnaHRcbiAgICAgIHR5cGU6IHNraWxsSWNvbnNbTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogc2tpbGxJY29ucy5sZW5ndGgpXS5uYW1lLFxuICAgICAgcG9pbnRzOiBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiA1MCkgKyAxMFxuICAgIH1cblxuICAgIHNldEdhbWVFbGVtZW50cyhwcmV2ID0+IFsuLi5wcmV2LCBuZXdFbGVtZW50XSlcblxuICAgIC8vIFJlbW92ZSBlbGVtZW50IGFmdGVyIDMgc2Vjb25kcyBpZiBub3QgY2xpY2tlZFxuICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgc2V0R2FtZUVsZW1lbnRzKHByZXYgPT4gcHJldi5maWx0ZXIoZWwgPT4gZWwuaWQgIT09IG5ld0VsZW1lbnQuaWQpKVxuICAgIH0sIDMwMDApXG4gIH0sIFtnYW1lQWN0aXZlXSlcblxuICBjb25zdCBoYW5kbGVFbGVtZW50Q2xpY2sgPSAoZWxlbWVudDogYW55KSA9PiB7XG4gICAgc2V0U2NvcmUocHJldiA9PiBwcmV2ICsgKGVsZW1lbnQucG9pbnRzICogbXVsdGlwbGllcikpXG4gICAgc2V0Q2xpY2tTdHJlYWsocHJldiA9PiBwcmV2ICsgMSlcbiAgICBzZXRHYW1lRWxlbWVudHMocHJldiA9PiBwcmV2LmZpbHRlcihlbCA9PiBlbC5pZCAhPT0gZWxlbWVudC5pZCkpXG5cbiAgICAvLyBDcmVhdGUgcGFydGljbGUgZWZmZWN0XG4gICAgY29uc3QgbmV3UGFydGljbGVzID0gQXJyYXkuZnJvbSh7bGVuZ3RoOiA1fSwgKF8sIGkpID0+ICh7XG4gICAgICBpZDogRGF0ZS5ub3coKSArIGksXG4gICAgICB4OiBlbGVtZW50LngsXG4gICAgICB5OiBlbGVtZW50LnksXG4gICAgICB2eDogKE1hdGgucmFuZG9tKCkgLSAwLjUpICogMTAsXG4gICAgICB2eTogKE1hdGgucmFuZG9tKCkgLSAwLjUpICogMTBcbiAgICB9KSlcbiAgICBzZXRQYXJ0aWNsZXMocHJldiA9PiBbLi4ucHJldiwgLi4ubmV3UGFydGljbGVzXSlcblxuICAgIC8vIENoZWNrIGZvciBhY2hpZXZlbWVudHNcbiAgICBjaGVja0FjaGlldmVtZW50cygpXG5cbiAgICAvLyBJbmNyZWFzZSBtdWx0aXBsaWVyIG9uIHN0cmVha1xuICAgIGlmIChjbGlja1N0cmVhayA+IDAgJiYgY2xpY2tTdHJlYWsgJSA1ID09PSAwKSB7XG4gICAgICBzZXRNdWx0aXBsaWVyKHByZXYgPT4gTWF0aC5taW4ocHJldiArIDAuNSwgNSkpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgY2hlY2tBY2hpZXZlbWVudHMgPSAoKSA9PiB7XG4gICAgY29uc3QgbmV3QWNoaWV2ZW1lbnRzID0gW11cblxuICAgIGlmIChzY29yZSA+PSAxMDAgJiYgIWFjaGlldmVtZW50cy5pbmNsdWRlcygnRmlyc3QgQ2VudHVyeScpKSB7XG4gICAgICBuZXdBY2hpZXZlbWVudHMucHVzaCgnRmlyc3QgQ2VudHVyeScpXG4gICAgfVxuICAgIGlmIChjbGlja1N0cmVhayA+PSAxMCAmJiAhYWNoaWV2ZW1lbnRzLmluY2x1ZGVzKCdTdHJlYWsgTWFzdGVyJykpIHtcbiAgICAgIG5ld0FjaGlldmVtZW50cy5wdXNoKCdTdHJlYWsgTWFzdGVyJylcbiAgICB9XG4gICAgaWYgKHNjb3JlID49IDUwMCAmJiAhYWNoaWV2ZW1lbnRzLmluY2x1ZGVzKCdSb2JvdCBPdmVybG9yZCcpKSB7XG4gICAgICBuZXdBY2hpZXZlbWVudHMucHVzaCgnUm9ib3QgT3ZlcmxvcmQnKVxuICAgIH1cblxuICAgIGlmIChuZXdBY2hpZXZlbWVudHMubGVuZ3RoID4gMCkge1xuICAgICAgc2V0QWNoaWV2ZW1lbnRzKHByZXYgPT4gWy4uLnByZXYsIC4uLm5ld0FjaGlldmVtZW50c10pXG4gICAgICBzZXRTaG93QWNoaWV2ZW1lbnQobmV3QWNoaWV2ZW1lbnRzWzBdKVxuICAgICAgc2V0VGltZW91dCgoKSA9PiBzZXRTaG93QWNoaWV2ZW1lbnQobnVsbCksIDMwMDApXG4gICAgfVxuICB9XG5cbiAgLy8gR2FtZSB0aW1lclxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChnYW1lQWN0aXZlICYmIHRpbWVMZWZ0ID4gMCkge1xuICAgICAgY29uc3QgdGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHNldFRpbWVMZWZ0KHByZXYgPT4gcHJldiAtIDEpLCAxMDAwKVxuICAgICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0aW1lcilcbiAgICB9IGVsc2UgaWYgKHRpbWVMZWZ0ID09PSAwKSB7XG4gICAgICBzZXRHYW1lQWN0aXZlKGZhbHNlKVxuICAgICAgc2V0R2FtZUVsZW1lbnRzKFtdKVxuICAgIH1cbiAgfSwgW2dhbWVBY3RpdmUsIHRpbWVMZWZ0XSlcblxuICAvLyBHZW5lcmF0ZSBnYW1lIGVsZW1lbnRzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGdhbWVBY3RpdmUpIHtcbiAgICAgIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwoZ2VuZXJhdGVHYW1lRWxlbWVudHMsIDE1MDApXG4gICAgICByZXR1cm4gKCkgPT4gY2xlYXJJbnRlcnZhbChpbnRlcnZhbClcbiAgICB9XG4gIH0sIFtnYW1lQWN0aXZlLCBnZW5lcmF0ZUdhbWVFbGVtZW50c10pXG5cbiAgLy8gVGV4dCByb3RhdGlvblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgc2V0Q3VycmVudFRleHQoKHByZXYpID0+IChwcmV2ICsgMSkgJSBoZXJvVGV4dHMubGVuZ3RoKVxuICAgIH0sIDMwMDApXG4gICAgcmV0dXJuICgpID0+IGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWwpXG4gIH0sIFtdKVxuXG4gIC8vIFBhcnRpY2xlIGFuaW1hdGlvblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgc2V0UGFydGljbGVzKHByZXYgPT4gcHJldi5maWx0ZXIocCA9PiBEYXRlLm5vdygpIC0gcC5pZCA8IDEwMDApKVxuICAgIH0sIDEwMClcbiAgICByZXR1cm4gKCkgPT4gY2xlYXJJbnRlcnZhbChpbnRlcnZhbClcbiAgfSwgW10pXG5cbiAgcmV0dXJuIChcbiAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgb3ZlcmZsb3ctaGlkZGVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tZGFyay04MDAgdmlhLWRhcmstOTAwIHRvLWRhcmstOTUwIHB0LTMyXCI+XG4gICAgICB7LyogQW5pbWF0ZWQgQmFja2dyb3VuZCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1jaXJjdWl0LXBhdHRlcm4gb3BhY2l0eS0xMFwiIC8+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1yb2JvY2VsbC15ZWxsb3cvNSB2aWEtdHJhbnNwYXJlbnQgdG8tcm9ib2NlbGwtb3JhbmdlLzVcIiAvPlxuXG4gICAgICB7LyogR2FtZSBIVUQgKi99XG4gICAgICB7Z2FtZUFjdGl2ZSAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTIwIGxlZnQtNCByaWdodC00IHotMjBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBiZy1ibGFjay81MCBiYWNrZHJvcC1ibHVyLW1kIHJvdW5kZWQteGwgcC00IGJvcmRlciBib3JkZXItcm9ib2NlbGwteWVsbG93LzMwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcm9ib2NlbGwteWVsbG93IGZvbnQtdGVjaCB0ZXh0LXNtXCI+U0NPUkU8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1ib2xkIHRleHQteGxcIj57c2NvcmUudG9Mb2NhbGVTdHJpbmcoKX08L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcm9ib2NlbGwtb3JhbmdlIGZvbnQtdGVjaCB0ZXh0LXNtXCI+U1RSRUFLPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtYm9sZCB0ZXh0LXhsXCI+e2NsaWNrU3RyZWFrfTwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1lbGVjdHJpYy1hbWJlciBmb250LXRlY2ggdGV4dC1zbVwiPk1VTFRJUExJRVI8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1ib2xkIHRleHQteGxcIj57bXVsdGlwbGllcn14PC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yZWQtNDAwIGZvbnQtdGVjaCB0ZXh0LXNtXCI+VElNRTwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1ib2xkIHRleHQtMnhsXCI+e3RpbWVMZWZ0fXM8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBBY2hpZXZlbWVudCBOb3RpZmljYXRpb24gKi99XG4gICAgICB7c2hvd0FjaGlldmVtZW50ICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMzIgbGVmdC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteC0xLzIgei0zMCBhbmltYXRlLWJvdW5jZVwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLXJvYm9jZWxsLXllbGxvdyB0by1yb2JvY2VsbC1vcmFuZ2Ugcm91bmRlZC14bCBwLTQgYm9yZGVyLTIgYm9yZGVyLXdoaXRlIHNoYWRvdy0yeGxcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgIDxUcm9waHkgY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LXRlY2ggZm9udC1ib2xkXCI+QWNoaWV2ZW1lbnQgVW5sb2NrZWQ6IHtzaG93QWNoaWV2ZW1lbnR9ITwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBHYW1lIEVsZW1lbnRzICovfVxuICAgICAge2dhbWVFbGVtZW50cy5tYXAoKGVsZW1lbnQpID0+IHtcbiAgICAgICAgY29uc3QgSWNvbkNvbXBvbmVudCA9IHNraWxsSWNvbnMuZmluZChpY29uID0+IGljb24ubmFtZSA9PT0gZWxlbWVudC50eXBlKSB8fCBDcHVcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICBrZXk9e2VsZW1lbnQuaWR9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB6LTEwIGN1cnNvci1wb2ludGVyIHRyYW5zZm9ybSBob3ZlcjpzY2FsZS0xMTAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgIHN0eWxlPXt7IGxlZnQ6IGAke2VsZW1lbnQueH0lYCwgdG9wOiBgJHtlbGVtZW50Lnl9JWAgfX1cbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUVsZW1lbnRDbGljayhlbGVtZW50KX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGdyb3VwXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IHJvdW5kZWQtZnVsbCBiZy1ncmFkaWVudC10by1iciBmcm9tLXJvYm9jZWxsLXllbGxvdyB0by1yb2JvY2VsbC1vcmFuZ2UgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYW5pbWF0ZS1wdWxzZSBob3ZlcjphbmltYXRlLWJvdW5jZSBzaGFkb3ctbGcgc2hhZG93LXJvYm9jZWxsLXllbGxvdy81MFwiPlxuICAgICAgICAgICAgICAgIDxJY29uQ29tcG9uZW50IGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIC10b3AtOCBsZWZ0LTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS14LTEvMiBiZy1ibGFjay84MCB0ZXh0LXJvYm9jZWxsLXllbGxvdyB0ZXh0LXhzIHB4LTIgcHktMSByb3VuZGVkIGZvbnQtdGVjaCBvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1vcGFjaXR5XCI+XG4gICAgICAgICAgICAgICAgK3tlbGVtZW50LnBvaW50c31cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCB3LTE2IGgtMTYgcm91bmRlZC1mdWxsIGJnLXJvYm9jZWxsLXllbGxvdy8zMCBhbmltYXRlLXBpbmdcIiAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIClcbiAgICAgIH0pfVxuXG4gICAgICB7LyogUGFydGljbGVzICovfVxuICAgICAge3BhcnRpY2xlcy5tYXAoKHBhcnRpY2xlKSA9PiAoXG4gICAgICAgIDxkaXZcbiAgICAgICAgICBrZXk9e3BhcnRpY2xlLmlkfVxuICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHctMiBoLTIgYmctcm9ib2NlbGwteWVsbG93IHJvdW5kZWQtZnVsbCBhbmltYXRlLXBpbmcgei0xMFwiXG4gICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgIGxlZnQ6IGAke3BhcnRpY2xlLnh9JWAsXG4gICAgICAgICAgICB0b3A6IGAke3BhcnRpY2xlLnl9JWAsXG4gICAgICAgICAgICB0cmFuc2Zvcm06IGB0cmFuc2xhdGUoJHtwYXJ0aWNsZS52eH1weCwgJHtwYXJ0aWNsZS52eX1weClgXG4gICAgICAgICAgfX1cbiAgICAgICAgLz5cbiAgICAgICkpfVxuXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHotMTAgbWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LTggdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LThcIj5cbiAgICAgICAgICB7LyogTWFpbiBIZWFkaW5nICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwiZm9udC10ZWNoIHRleHQtNHhsIG1kOnRleHQtNnhsIGxnOnRleHQtN3hsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIGxlYWRpbmctdGlnaHRcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmxvY2sgYmctZ3JhZGllbnQtdG8tciBmcm9tLXJvYm9jZWxsLXllbGxvdyB2aWEtcm9ib2NlbGwtb3JhbmdlIHRvLWVsZWN0cmljLWFtYmVyIGJnLWNsaXAtdGV4dCB0ZXh0LXRyYW5zcGFyZW50XCI+XG4gICAgICAgICAgICAgICAgUm9ib0NlbGxcbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LTJ4bCBtZDp0ZXh0LTN4bCBsZzp0ZXh0LTR4bCBmb250LW5vcm1hbCB0ZXh0LWdyYXktMzAwIG10LTJcIj5cbiAgICAgICAgICAgICAgICBOSVQgRHVyZ2FwdXJcbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9oMT5cblxuICAgICAgICAgICAgey8qIEFuaW1hdGVkIFRhZ2xpbmUgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMTYgbWQ6aC0yMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwiZm9udC10ZWNoIHRleHQteGwgbWQ6dGV4dC0zeGwgbGc6dGV4dC00eGwgZm9udC1zZW1pYm9sZCB0ZXh0LXRyYW5zcGFyZW50IGJnLWNsaXAtdGV4dCBiZy1ncmFkaWVudC10by1yIGZyb20tZWxlY3RyaWMteWVsbG93IHZpYS1yb2JvY2VsbC1vcmFuZ2UgdG8tZWxlY3RyaWMtYW1iZXIgYW5pbWF0ZS1wdWxzZVwiPlxuICAgICAgICAgICAgICAgIHtoZXJvVGV4dHNbY3VycmVudFRleHRdfVxuICAgICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogSW50ZXJhY3RpdmUgR2FtZSBEZXNjcmlwdGlvbiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTR4bCBteC1hdXRvXCI+XG4gICAgICAgICAgICB7IWdhbWVBY3RpdmUgPyAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyBtZDp0ZXh0LXhsIHRleHQtZ3JheS0zMDAgbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgICAgICDwn46uIFdlbGNvbWUgdG8gUm9ib0NlbGwncyBJbnRlcmFjdGl2ZSBDaGFsbGVuZ2UhIFRlc3QgeW91ciByZWZsZXhlcyBhbmQgdW5sb2NrIGFjaGlldmVtZW50cyB3aGlsZSBsZWFybmluZyBhYm91dCBvdXIgYW1hemluZyByb2JvdGljcyBqb3VybmV5LlxuICAgICAgICAgICAgICAgICAgQ2xpY2sgdGhlIGZsb2F0aW5nIHRlY2ggaWNvbnMgdG8gc2NvcmUgcG9pbnRzIGFuZCBkaXNjb3ZlciB3aGF0IG1ha2VzIHVzIHRoZSB1bHRpbWF0ZSByb2JvdGljcyBjbHViISDwn5qAXG4gICAgICAgICAgICAgICAgPC9wPlxuXG4gICAgICAgICAgICAgICAgey8qIFN0YXRzIERpc3BsYXkgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIG1kOmdyaWQtY29scy00IGdhcC00IG10LThcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1yb2JvY2VsbC15ZWxsb3cvMjAgdG8tcm9ib2NlbGwtb3JhbmdlLzIwIHJvdW5kZWQteGwgcC00IGJvcmRlciBib3JkZXItcm9ib2NlbGwteWVsbG93LzMwIGJhY2tkcm9wLWJsdXItc21cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBtYi0yXCI+8J+PhjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcm9ib2NlbGwteWVsbG93IGZvbnQtdGVjaCB0ZXh0LXNtXCI+QUNISUVWRU1FTlRTPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LWJvbGQgdGV4dC14bFwiPnthY2hpZXZlbWVudHMubGVuZ3RofS8xMDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLWJyIGZyb20tcm9ib2NlbGwtb3JhbmdlLzIwIHRvLWVsZWN0cmljLWFtYmVyLzIwIHJvdW5kZWQteGwgcC00IGJvcmRlciBib3JkZXItcm9ib2NlbGwtb3JhbmdlLzMwIGJhY2tkcm9wLWJsdXItc21cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBtYi0yXCI+4pqhPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yb2JvY2VsbC1vcmFuZ2UgZm9udC10ZWNoIHRleHQtc21cIj5ISUdIIFNDT1JFPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LWJvbGQgdGV4dC14bFwiPntNYXRoLm1heChzY29yZSwgMCl9PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1lbGVjdHJpYy1hbWJlci8yMCB0by1yb2JvY2VsbC15ZWxsb3cvMjAgcm91bmRlZC14bCBwLTQgYm9yZGVyIGJvcmRlci1lbGVjdHJpYy1hbWJlci8zMCBiYWNrZHJvcC1ibHVyLXNtXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgbWItMlwiPvCfjq88L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWVsZWN0cmljLWFtYmVyIGZvbnQtdGVjaCB0ZXh0LXNtXCI+QkVTVCBTVFJFQUs8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtYm9sZCB0ZXh0LXhsXCI+e01hdGgubWF4KGNsaWNrU3RyZWFrLCAwKX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1iciBmcm9tLW5lb24tYmx1ZS8yMCB0by1yb2JvY2VsbC15ZWxsb3cvMjAgcm91bmRlZC14bCBwLTQgYm9yZGVyIGJvcmRlci1uZW9uLWJsdWUvMzAgYmFja2Ryb3AtYmx1ci1zbVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIG1iLTJcIj7wn5qAPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1uZW9uLWJsdWUgZm9udC10ZWNoIHRleHQtc21cIj5MRVZFTDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1ib2xkIHRleHQteGxcIj57bGV2ZWx9PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCB0ZXh0LXJvYm9jZWxsLXllbGxvdyBmb250LXRlY2ggYW5pbWF0ZS1wdWxzZVwiPlxuICAgICAgICAgICAgICAgICAg8J+OryBDbGljayB0aGUgZmxvYXRpbmcgaWNvbnMgdG8gc2NvcmUgcG9pbnRzISDwn46vXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgbXQtMlwiPlxuICAgICAgICAgICAgICAgICAgQnVpbGQgc3RyZWFrcyBmb3IgbXVsdGlwbGllcnMg4oCiIFVubG9jayBhY2hpZXZlbWVudHMg4oCiIEJlY29tZSBhIFJvYm9DZWxsIGxlZ2VuZCFcbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBHYW1lIENvbnRyb2wgQ2VudGVyICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMTIgcmVsYXRpdmVcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBpdGVtcy1jZW50ZXIgc3BhY2UteC04XCI+XG4gICAgICAgICAgICAgIHshZ2FtZUFjdGl2ZSA/IChcbiAgICAgICAgICAgICAgICAvKiBTdGFydCBHYW1lIEJ1dHRvbiAqL1xuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgZ3JvdXAgY3Vyc29yLXBvaW50ZXJcIiBvbkNsaWNrPXtzdGFydEdhbWV9PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTMyIGgtMzIgbWQ6dy00MCBtZDpoLTQwIHJvdW5kZWQtZnVsbCBib3JkZXItNCBib3JkZXItcm9ib2NlbGwteWVsbG93IGJnLWdyYWRpZW50LXRvLWJyIGZyb20tcm9ib2NlbGwteWVsbG93IHZpYS1yb2JvY2VsbC1vcmFuZ2UgdG8tZWxlY3RyaWMtYW1iZXIgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZ3JvdXAtaG92ZXI6c2NhbGUtMTEwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMCBzaGFkb3ctMnhsIGFuaW1hdGUtcHVsc2UgaG92ZXI6YW5pbWF0ZS1ib3VuY2VcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxHYW1lcGFkMiBjbGFzc05hbWU9XCJoLTEyIHctMTIgbWQ6aC0xNiBtZDp3LTE2IHRleHQtd2hpdGUgbXgtYXV0byBtYi0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC10ZWNoIGZvbnQtYm9sZCB0ZXh0LXNtIG1kOnRleHQtYmFzZVwiPlNUQVJUIEdBTUU8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCB3LTMyIGgtMzIgbWQ6dy00MCBtZDpoLTQwIHJvdW5kZWQtZnVsbCBiZy1ncmFkaWVudC10by1iciBmcm9tLXJvYm9jZWxsLXllbGxvdyB0by1yb2JvY2VsbC1vcmFuZ2Ugb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMzAgZ3JvdXAtaG92ZXI6YW5pbWF0ZS1waW5nXCIgLz5cblxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtYm90dG9tLTEyIGxlZnQtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXgtMS8yIHRleHQtcm9ib2NlbGwteWVsbG93IHRleHQtc20gZm9udC10ZWNoIG9wYWNpdHktMCBncm91cC1ob3ZlcjpvcGFjaXR5LTEwMCB0cmFuc2l0aW9uLW9wYWNpdHkgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAg8J+OriBDbGljayB0byBzdGFydCB0aGUgY2hhbGxlbmdlIVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgLyogR2FtZSBBY3RpdmUgRGlzcGxheSAqL1xuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yNCBoLTI0IG1kOnctMzIgbWQ6aC0zMiByb3VuZGVkLWZ1bGwgYm9yZGVyLTQgYm9yZGVyLWdyZWVuLTQwMCBiZy1ncmFkaWVudC10by1iciBmcm9tLWdyZWVuLTQwMCB0by1ncmVlbi02MDAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYW5pbWF0ZS1wdWxzZSBzaGFkb3ctMnhsXCI+XG4gICAgICAgICAgICAgICAgICAgIDxUYXJnZXQgY2xhc3NOYW1lPVwiaC0xMiB3LTEyIG1kOmgtMTYgbWQ6dy0xNiB0ZXh0LXdoaXRlIGFuaW1hdGUtc3BpblwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LWdyZWVuLTQwMCBmb250LXRlY2ggZm9udC1ib2xkIGFuaW1hdGUtYm91bmNlXCI+XG4gICAgICAgICAgICAgICAgICAgIEdBTUUgQUNUSVZFIVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIEFjaGlldmVtZW50IFNob3djYXNlICovfVxuICAgICAgICAgICAge2FjaGlldmVtZW50cy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC04XCI+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtcm9ib2NlbGwteWVsbG93IGZvbnQtdGVjaCB0ZXh0LWxnIG1iLTRcIj7wn4+GIFlvdXIgQWNoaWV2ZW1lbnRzPC9oMz5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGp1c3RpZnktY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICB7YWNoaWV2ZW1lbnRzLm1hcCgoYWNoaWV2ZW1lbnQsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLXJvYm9jZWxsLXllbGxvdy8yMCB0by1yb2JvY2VsbC1vcmFuZ2UvMjAgYmFja2Ryb3AtYmx1ci1zbSByb3VuZGVkLWZ1bGwgcHgtNCBweS0yIGJvcmRlciBib3JkZXItcm9ib2NlbGwteWVsbG93LzUwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yb2JvY2VsbC15ZWxsb3cgZm9udC10ZWNoIHRleHQtc21cIj57YWNoaWV2ZW1lbnR9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBGbG9hdGluZyBJbmZvIEJhZGdlcyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTAgbGVmdC0wIHctZnVsbCBoLWZ1bGwgcG9pbnRlci1ldmVudHMtbm9uZVwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC00IGxlZnQtMS80IGFuaW1hdGUtZmxvYXQgYmctcm9ib2NlbGwteWVsbG93LzIwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC1mdWxsIHB4LTMgcHktMSB0ZXh0LXJvYm9jZWxsLXllbGxvdyB0ZXh0LXhzIGZvbnQtYm9sZFwiPlxuICAgICAgICAgICAgICAgIPCfj4YgUm9ib2NvbiBDaGFtcGlvbnNcbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTggcmlnaHQtMS80IGFuaW1hdGUtZmxvYXQgYmctcm9ib2NlbGwtb3JhbmdlLzIwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC1mdWxsIHB4LTMgcHktMSB0ZXh0LXJvYm9jZWxsLW9yYW5nZSB0ZXh0LXhzIGZvbnQtYm9sZFwiIHN0eWxlPXt7IGFuaW1hdGlvbkRlbGF5OiAnMXMnIH19PlxuICAgICAgICAgICAgICAgIOKaoSA1ODcrIEFjdGl2ZSBNZW1iZXJzXG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS00IGxlZnQtMS8zIGFuaW1hdGUtZmxvYXQgYmctZWxlY3RyaWMtYW1iZXIvMjAgYmFja2Ryb3AtYmx1ci1zbSByb3VuZGVkLWZ1bGwgcHgtMyBweS0xIHRleHQtZWxlY3RyaWMtYW1iZXIgdGV4dC14cyBmb250LWJvbGRcIiBzdHlsZT17eyBhbmltYXRpb25EZWxheTogJzJzJyB9fT5cbiAgICAgICAgICAgICAgICDwn5qAIDMwKyBMaXZlIFByb2plY3RzXG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS04IHJpZ2h0LTEvMyBhbmltYXRlLWZsb2F0IGJnLW5lb24tYmx1ZS8yMCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtZnVsbCBweC0zIHB5LTEgdGV4dC1uZW9uLWJsdWUgdGV4dC14cyBmb250LWJvbGRcIiBzdHlsZT17eyBhbmltYXRpb25EZWxheTogJzNzJyB9fT5cbiAgICAgICAgICAgICAgICDwn46vIEpvaW4gdGhlIEFkdmVudHVyZSFcbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFNjcm9sbCBJbmRpY2F0b3IgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS04IGxlZnQtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXgtMS8yIGFuaW1hdGUtYm91bmNlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy02IGgtMTAgYm9yZGVyLTIgYm9yZGVyLXJvYm9jZWxsLXllbGxvdyByb3VuZGVkLWZ1bGwgZmxleCBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xIGgtMyBiZy1ncmFkaWVudC10by1iIGZyb20tcm9ib2NlbGwteWVsbG93IHRvLXJvYm9jZWxsLW9yYW5nZSByb3VuZGVkLWZ1bGwgbXQtMiBhbmltYXRlLXB1bHNlXCIgLz5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L3NlY3Rpb24+XG4gIClcbn1cblxuZXhwb3J0IGRlZmF1bHQgSG9tZVNlY3Rpb25cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUNhbGxiYWNrIiwiQ3B1IiwiWmFwIiwiVHJvcGh5IiwiVGFyZ2V0IiwiR2FtZXBhZDIiLCJSb2NrZXQiLCJCcmFpbiIsIkNvZGUiLCJXcmVuY2giLCJIb21lU2VjdGlvbiIsImN1cnJlbnRUZXh0Iiwic2V0Q3VycmVudFRleHQiLCJzY29yZSIsInNldFNjb3JlIiwibGV2ZWwiLCJzZXRMZXZlbCIsImNsaWNrU3RyZWFrIiwic2V0Q2xpY2tTdHJlYWsiLCJhY2hpZXZlbWVudHMiLCJzZXRBY2hpZXZlbWVudHMiLCJzaG93QWNoaWV2ZW1lbnQiLCJzZXRTaG93QWNoaWV2ZW1lbnQiLCJnYW1lRWxlbWVudHMiLCJzZXRHYW1lRWxlbWVudHMiLCJwb3dlclVwcyIsInNldFBvd2VyVXBzIiwibXVsdGlwbGllciIsInNldE11bHRpcGxpZXIiLCJ0aW1lTGVmdCIsInNldFRpbWVMZWZ0IiwiZ2FtZUFjdGl2ZSIsInNldEdhbWVBY3RpdmUiLCJwYXJ0aWNsZXMiLCJzZXRQYXJ0aWNsZXMiLCJoZXJvVGV4dHMiLCJza2lsbEljb25zIiwic3RhcnRHYW1lIiwiZ2VuZXJhdGVHYW1lRWxlbWVudHMiLCJuZXdFbGVtZW50IiwiaWQiLCJEYXRlIiwibm93IiwieCIsIk1hdGgiLCJyYW5kb20iLCJ5IiwidHlwZSIsImZsb29yIiwibGVuZ3RoIiwibmFtZSIsInBvaW50cyIsInByZXYiLCJzZXRUaW1lb3V0IiwiZmlsdGVyIiwiZWwiLCJoYW5kbGVFbGVtZW50Q2xpY2siLCJlbGVtZW50IiwibmV3UGFydGljbGVzIiwiQXJyYXkiLCJmcm9tIiwiXyIsImkiLCJ2eCIsInZ5IiwiY2hlY2tBY2hpZXZlbWVudHMiLCJtaW4iLCJuZXdBY2hpZXZlbWVudHMiLCJpbmNsdWRlcyIsInB1c2giLCJ0aW1lciIsImNsZWFyVGltZW91dCIsImludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJjbGVhckludGVydmFsIiwicCIsInNlY3Rpb24iLCJjbGFzc05hbWUiLCJkaXYiLCJ0b0xvY2FsZVN0cmluZyIsInNwYW4iLCJtYXAiLCJJY29uQ29tcG9uZW50IiwiZmluZCIsImljb24iLCJzdHlsZSIsImxlZnQiLCJ0b3AiLCJvbkNsaWNrIiwicGFydGljbGUiLCJ0cmFuc2Zvcm0iLCJoMSIsImgyIiwibWF4IiwiaDMiLCJhY2hpZXZlbWVudCIsImluZGV4IiwiYW5pbWF0aW9uRGVsYXkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/Hero.tsx\n"));

/***/ })

});
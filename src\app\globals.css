@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Orbitron:wght@400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 31, 41, 55;
  --background-end-rgb: 17, 24, 39;
  --accent-yellow: 251, 191, 36;
  --accent-orange: 249, 115, 22;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  scroll-behavior: smooth;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
  font-family: 'Inter', sans-serif;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1f2937;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #fbbf24, #f97316);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #f59e0b, #ea580c);
}

/* Glassmorphism utility classes */
@layer utilities {
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
  
  .glass-dark {
    @apply bg-black/20 backdrop-blur-md border border-white/10;
  }
  
  .text-glow {
    text-shadow: 0 0 10px currentColor;
  }
  
  .neon-border {
    box-shadow: 0 0 5px #fbbf24, 0 0 10px #fbbf24, 0 0 15px #fbbf24;
  }

  .hover-glow {
    transition: all 0.3s ease;
  }

  .hover-glow:hover {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.5);
    transform: translateY(-2px);
  }

  /* Animation keyframes */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-spin-slow {
    animation: spin 8s linear infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }

  /* Pulse animation for interactive elements */
  .pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite;
  }

  @keyframes pulseGlow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(251, 191, 36, 0.2);
    }
    50% {
      box-shadow: 0 0 40px rgba(251, 191, 36, 0.4);
    }
  }
}

/* Animation classes */
@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-robocell-yellow to-robocell-orange text-dark-900 font-semibold py-3 px-6 rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-robocell-yellow/25 hover:from-robocell-orange hover:to-electric-amber;
  }

  .btn-secondary {
    @apply border-2 border-robocell-yellow text-robocell-yellow font-semibold py-3 px-6 rounded-lg transition-all duration-300 hover:scale-105 hover:bg-robocell-yellow hover:text-dark-900 hover:shadow-2xl hover:shadow-robocell-yellow/25;
  }
  
  .card {
    @apply glass-dark rounded-xl p-6 hover-glow transition-all duration-300;
  }
  
  .section-padding {
    @apply py-16 px-4 sm:px-6 lg:px-8;
  }
}

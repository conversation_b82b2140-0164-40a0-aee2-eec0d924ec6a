'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { ArrowRight, Play, <PERSON>pu, Zap, Cog, Sparkles } from 'lucide-react'

const Hero = () => {
  const [currentText, setCurrentText] = useState(0)
  const [robotClicked, setRobotClicked] = useState(false)
  const [sparkles, setSparkles] = useState<Array<{id: number, x: number, y: number}>>([])

  const handleRobotClick = () => {
    setRobotClicked(true)
    // Create sparkle effect
    const newSparkles = Array.from({length: 8}, (_, i) => ({
      id: Date.now() + i,
      x: Math.random() * 200 - 100,
      y: Math.random() * 200 - 100
    }))
    setSparkles(newSparkles)

    // Reset after animation
    setTimeout(() => {
      setRobotClicked(false)
      setSparkles([])
    }, 2000)
  }
  
  const heroTexts = [
    "Ideate, Innovate, Inspire! ⚡",
    "Where Code Meets Creativity 🤖",
    "Building the Future, One Bot at a Time 🚀",
    "Robocon Champions & Tech Innovators 🏆"
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentText((prev) => (prev + 1) % heroTexts.length)
    }, 3000)
    return () => clearInterval(interval)
  }, [])

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-dark-800 via-dark-900 to-dark-950 pt-32">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-circuit-pattern opacity-10" />
      <div className="absolute inset-0 bg-gradient-to-br from-robocell-yellow/5 via-transparent to-robocell-orange/5" />
      
      {/* Floating Elements */}
      <div className="absolute top-20 left-10 animate-float">
        <Cpu className="h-12 w-12 text-robocell-yellow opacity-20" />
      </div>
      <div className="absolute top-40 right-20 animate-float" style={{ animationDelay: '1s' }}>
        <Cog className="h-16 w-16 text-robocell-orange opacity-15 animate-spin-slow" />
      </div>
      <div className="absolute bottom-40 left-20 animate-float" style={{ animationDelay: '2s' }}>
        <Zap className="h-10 w-10 text-electric-yellow opacity-30" />
      </div>
      <div className="absolute top-1/3 right-1/4 animate-float" style={{ animationDelay: '3s' }}>
        <div className="w-8 h-8 rounded-full bg-gradient-to-br from-robocell-yellow to-robocell-orange opacity-20" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="space-y-8">
          {/* Main Heading */}
          <div className="space-y-4">
            <h1 className="font-tech text-4xl md:text-6xl lg:text-7xl font-bold text-white leading-tight">
              <span className="block bg-gradient-to-r from-robocell-yellow via-robocell-orange to-electric-amber bg-clip-text text-transparent">
                RoboCell
              </span>
              <span className="block text-2xl md:text-3xl lg:text-4xl font-normal text-gray-300 mt-2">
                NIT Durgapur
              </span>
            </h1>
            
            {/* Animated Tagline */}
            <div className="h-16 md:h-20 flex items-center justify-center">
              <h2 className="font-tech text-xl md:text-3xl lg:text-4xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-electric-yellow via-robocell-orange to-electric-amber animate-pulse">
                {heroTexts[currentText]}
              </h2>
            </div>
          </div>

          {/* Description */}
          <p className="max-w-3xl mx-auto text-lg md:text-xl text-gray-300 leading-relaxed">
            🤖 Welcome to RoboCell - where engineering students turn wild ideas into reality!
            Join us for epic robotics projects, Robocon adventures, and mind-blowing tech innovations.
            Ready to code, build, and conquer? Let's make some robot magic! ✨
          </p>

          {/* Interactive Robot Animation */}
          <div className="mt-12 relative">
            <div className="flex justify-center items-center space-x-8">
              {/* Interactive Robot Face */}
              <div className="relative group cursor-pointer" onClick={handleRobotClick}>
                <div className={`w-24 h-24 md:w-32 md:h-32 rounded-full border-4 border-robocell-yellow bg-gradient-to-br from-robocell-yellow via-robocell-orange to-electric-amber flex items-center justify-center group-hover:scale-110 transition-all duration-500 shadow-2xl ${robotClicked ? 'animate-bounce' : 'animate-pulse'}`}>
                  <div className="relative">
                    {/* Robot Eyes */}
                    <div className="flex space-x-3 mb-2">
                      <div className={`w-3 h-3 md:w-4 md:h-4 bg-white rounded-full ${robotClicked ? 'animate-ping' : 'animate-pulse'}`}></div>
                      <div className={`w-3 h-3 md:w-4 md:h-4 bg-white rounded-full ${robotClicked ? 'animate-ping' : 'animate-pulse'}`} style={{ animationDelay: '0.5s' }}></div>
                    </div>
                    {/* Lightning Bolt */}
                    <Zap className={`h-6 w-6 md:h-8 md:w-8 text-white mx-auto ${robotClicked ? 'animate-spin' : 'animate-bounce'}`} />
                  </div>
                </div>
                <div className="absolute inset-0 w-24 h-24 md:w-32 md:h-32 rounded-full bg-gradient-to-br from-robocell-yellow to-robocell-orange opacity-0 group-hover:opacity-30 group-hover:animate-ping" />

                {/* Sparkle Effects */}
                {sparkles.map((sparkle) => (
                  <Sparkles
                    key={sparkle.id}
                    className="absolute h-4 w-4 text-robocell-yellow animate-ping"
                    style={{
                      left: `50%`,
                      top: `50%`,
                      transform: `translate(${sparkle.x}px, ${sparkle.y}px)`,
                      animationDuration: '1s'
                    }}
                  />
                ))}

                <div className={`absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-robocell-yellow text-sm font-tech transition-opacity ${robotClicked ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}`}>
                  {robotClicked ? '🎉 Welcome to RoboCell!' : 'Click me! 🤖'}
                </div>
              </div>
            </div>

            {/* Floating Achievement Badges */}
            <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
              <div className="absolute top-4 left-1/4 animate-float bg-robocell-yellow/20 backdrop-blur-sm rounded-full px-3 py-1 text-robocell-yellow text-xs font-bold">
                🏆 Robocon 2024
              </div>
              <div className="absolute top-8 right-1/4 animate-float bg-robocell-orange/20 backdrop-blur-sm rounded-full px-3 py-1 text-robocell-orange text-xs font-bold" style={{ animationDelay: '1s' }}>
                ⚡ 587+ Members
              </div>
              <div className="absolute bottom-4 left-1/3 animate-float bg-electric-amber/20 backdrop-blur-sm rounded-full px-3 py-1 text-electric-amber text-xs font-bold" style={{ animationDelay: '2s' }}>
                🚀 30+ Projects
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-robocell-yellow rounded-full flex justify-center">
          <div className="w-1 h-3 bg-gradient-to-b from-robocell-yellow to-robocell-orange rounded-full mt-2 animate-pulse" />
        </div>
      </div>
    </section>
  )
}

export default Hero

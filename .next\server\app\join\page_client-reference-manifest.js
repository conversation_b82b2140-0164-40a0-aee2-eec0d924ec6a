globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/join/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Header.tsx":{"*":{"id":"(ssr)/./src/components/layout/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Hero.tsx":{"*":{"id":"(ssr)/./src/components/sections/Hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/LogoShowcase.tsx":{"*":{"id":"(ssr)/./src/components/sections/LogoShowcase.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Memories.tsx":{"*":{"id":"(ssr)/./src/components/sections/Memories.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/MiniQuiz.tsx":{"*":{"id":"(ssr)/./src/components/sections/MiniQuiz.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/JoinForm.tsx":{"*":{"id":"(ssr)/./src/components/sections/JoinForm.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\node_modules\\next\\dist\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\node_modules\\next\\dist\\esm\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Orbitron\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-orbitron\"}],\"variableName\":\"orbitron\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Orbitron\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-orbitron\"}],\"variableName\":\"orbitron\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/join/page","static/chunks/app/join/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/join/page","static/chunks/app/join/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\node_modules\\next\\dist\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/join/page","static/chunks/app/join/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/join/page","static/chunks/app/join/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\src\\components\\layout\\Header.tsx":{"id":"(app-pages-browser)/./src/components/layout/Header.tsx","name":"*","chunks":["app/join/page","static/chunks/app/join/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\src\\components\\sections\\Hero.tsx":{"id":"(app-pages-browser)/./src/components/sections/Hero.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\src\\components\\sections\\LogoShowcase.tsx":{"id":"(app-pages-browser)/./src/components/sections/LogoShowcase.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\src\\components\\sections\\Memories.tsx":{"id":"(app-pages-browser)/./src/components/sections/Memories.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\src\\components\\sections\\MiniQuiz.tsx":{"id":"(app-pages-browser)/./src/components/sections/MiniQuiz.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\src\\components\\sections\\JoinForm.tsx":{"id":"(app-pages-browser)/./src/components/sections/JoinForm.tsx","name":"*","chunks":["app/join/page","static/chunks/app/join/page.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\src\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\src\\app\\page":[],"C:\\Users\\<USER>\\Documents\\augment-projects\\roboCell\\src\\app\\join\\page":[]}}
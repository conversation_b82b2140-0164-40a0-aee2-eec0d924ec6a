'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, Zap, <PERSON>pu, Cog, Code, Wrench, Trophy, Users, Rocket, Brain } from 'lucide-react'

const InteractiveShowcase = () => {
  const [activeSkill, setActiveSkill] = useState(0)
  const [isHovered, setIsHovered] = useState(false)

  const skills = [
    { icon: Code, name: "Programming", description: "Python, C++, Arduino", color: "from-robocell-yellow to-robocell-orange" },
    { icon: Wrench, name: "Hardware", description: "PCB Design, 3D Printing", color: "from-robocell-orange to-electric-amber" },
    { icon: Brain, name: "AI/ML", description: "Computer Vision, Robotics", color: "from-electric-amber to-robocell-yellow" },
    { icon: Trophy, name: "Competitions", description: "Robocon, Hackathons", color: "from-robocell-yellow to-robocell-orange" }
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      if (!isHovered) {
        setActiveSkill((prev) => (prev + 1) % skills.length)
      }
    }, 2000)
    return () => clearInterval(interval)
  }, [isHovered, skills.length])

  return (
    <section className="section-padding bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-10 animate-float">
          <Cpu className="h-8 w-8 text-robocell-yellow" />
        </div>
        <div className="absolute top-40 right-20 animate-float" style={{ animationDelay: '1s' }}>
          <Cog className="h-12 w-12 text-robocell-orange animate-spin-slow" />
        </div>
        <div className="absolute bottom-40 left-20 animate-float" style={{ animationDelay: '2s' }}>
          <Zap className="h-6 w-6 text-electric-yellow" />
        </div>
      </div>

      <div className="max-w-7xl mx-auto text-center relative z-10">
        {/* Interactive Skills Showcase */}
        <div className="mb-16">
          <h2 className="font-tech text-3xl md:text-5xl font-bold text-white mb-4">
            What You'll <span className="bg-gradient-to-r from-robocell-yellow to-robocell-orange bg-clip-text text-transparent">Master</span>
          </h2>
          <p className="text-lg text-gray-300 mb-12">
            Hover over the skills to see what awaits you in RoboCell! 🚀
          </p>

          {/* Interactive Skills Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
            {skills.map((skill, index) => {
              const IconComponent = skill.icon
              return (
                <div
                  key={index}
                  className={`relative p-6 rounded-xl cursor-pointer transition-all duration-500 transform hover:scale-105 ${
                    activeSkill === index ? 'bg-gradient-to-br from-robocell-yellow/20 to-robocell-orange/20 border-2 border-robocell-yellow' : 'bg-dark-800/50 border border-gray-700'
                  }`}
                  onMouseEnter={() => {
                    setActiveSkill(index)
                    setIsHovered(true)
                  }}
                  onMouseLeave={() => setIsHovered(false)}
                >
                  <div className={`w-16 h-16 rounded-full bg-gradient-to-br ${skill.color} flex items-center justify-center mx-auto mb-4 transition-all duration-300 ${
                    activeSkill === index ? 'animate-pulse' : ''
                  }`}>
                    <IconComponent className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="font-tech text-lg font-bold text-white mb-2">{skill.name}</h3>
                  <p className="text-sm text-gray-300">{skill.description}</p>

                  {activeSkill === index && (
                    <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-robocell-yellow/10 to-robocell-orange/10 animate-pulse" />
                  )}
                </div>
              )
            })}
          </div>
        </div>

        {/* Interactive Challenge Section */}
        <div className="max-w-4xl mx-auto">
          <h2 className="font-tech text-3xl md:text-5xl font-bold text-white mb-6">
            Ready for the <span className="bg-gradient-to-r from-robocell-yellow to-robocell-orange bg-clip-text text-transparent">Challenge?</span>
          </h2>

          <p className="text-lg md:text-xl text-gray-300 leading-relaxed mb-12">
            Think you've got what it takes to build the future? Let's see if you can solve this! 🤔⚡
          </p>

          {/* Interactive Coding Challenge */}
          <div className="bg-dark-800/50 rounded-xl p-8 border border-robocell-yellow/30 mb-12">
            <h3 className="font-tech text-xl font-bold text-robocell-yellow mb-4">🧩 Quick Challenge</h3>
            <div className="bg-dark-900 rounded-lg p-6 font-mono text-sm text-green-400 mb-6">
              <div className="text-gray-400 mb-2">// Can you spot the bug in this Arduino code?</div>
              <div className="text-blue-400">void <span className="text-yellow-400">setup</span>() {`{`}</div>
              <div className="ml-4 text-white">Serial.begin(<span className="text-orange-400">9600</span>);</div>
              <div className="text-blue-400">{`}`}</div>
              <div className="mt-2 text-blue-400">void <span className="text-yellow-400">loop</span>() {`{`}</div>
              <div className="ml-4 text-white">int sensorValue = analogRead(<span className="text-orange-400">A0</span>);</div>
              <div className="ml-4 text-white">Serial.print(<span className="text-green-300">"Sensor: "</span>);</div>
              <div className="ml-4 text-white">Serial.println(sensorValue);</div>
              <div className="ml-4 text-red-400">delay(<span className="text-orange-400">10</span>); // 🐛 Bug here!</div>
              <div className="text-blue-400">{`}`}</div>
            </div>
            <div className="text-center">
              <button className="bg-gradient-to-r from-robocell-yellow to-robocell-orange text-dark-900 px-6 py-3 rounded-lg font-bold hover:scale-105 transition-transform duration-300">
                Think you know? Join us to find out! 🚀
              </button>
            </div>
          </div>

          {/* Fun Facts Carousel */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gradient-to-br from-robocell-yellow/10 to-robocell-orange/10 rounded-xl p-6 border border-robocell-yellow/30">
              <div className="text-4xl mb-3">🤖</div>
              <h4 className="font-tech text-lg font-bold text-white mb-2">Did You Know?</h4>
              <p className="text-gray-300 text-sm">Our robots have walked over 10,000 steps in competitions!</p>
            </div>

            <div className="bg-gradient-to-br from-robocell-orange/10 to-electric-amber/10 rounded-xl p-6 border border-robocell-orange/30">
              <div className="text-4xl mb-3">⚡</div>
              <h4 className="font-tech text-lg font-bold text-white mb-2">Lightning Fast</h4>
              <p className="text-gray-300 text-sm">We can prototype a robot in just 48 hours during hackathons!</p>
            </div>

            <div className="bg-gradient-to-br from-electric-amber/10 to-robocell-yellow/10 rounded-xl p-6 border border-electric-amber/30">
              <div className="text-4xl mb-3">🏆</div>
              <h4 className="font-tech text-lg font-bold text-white mb-2">Champions</h4>
              <p className="text-gray-300 text-sm">Multiple Robocon victories and counting!</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default InteractiveShowcase

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/Hero.tsx":
/*!******************************************!*\
  !*** ./src/components/sections/Hero.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cpu_Rocket_Sparkles_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cpu,Rocket,Sparkles,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cpu_Rocket_Sparkles_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cpu,Rocket,Sparkles,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cpu_Rocket_Sparkles_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cpu,Rocket,Sparkles,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cpu_Rocket_Sparkles_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cpu,Rocket,Sparkles,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cpu_Rocket_Sparkles_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cpu,Rocket,Sparkles,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cpu_Rocket_Sparkles_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cpu,Rocket,Sparkles,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cpu_Rocket_Sparkles_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cpu,Rocket,Sparkles,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cpu_Rocket_Sparkles_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cpu,Rocket,Sparkles,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst Hero = ()=>{\n    _s();\n    const [currentText, setCurrentText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [level, setLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [clickStreak, setClickStreak] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [achievements, setAchievements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showAchievement, setShowAchievement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [gameElements, setGameElements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [powerUps, setPowerUps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [multiplier, setMultiplier] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(30);\n    const [gameActive, setGameActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [particles, setParticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const heroTexts = [\n        \"\\uD83C\\uDFAE Ready to Level Up Your Skills?\",\n        \"\\uD83D\\uDE80 Join the Ultimate Tech Adventure!\",\n        \"⚡ Unlock Your Robotics Potential!\",\n        \"\\uD83C\\uDFC6 Become a RoboCell Legend!\"\n    ];\n    const skillIcons = [\n        _barrel_optimize_names_Brain_Code_Cpu_Rocket_Sparkles_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        _barrel_optimize_names_Brain_Code_Cpu_Rocket_Sparkles_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        _barrel_optimize_names_Brain_Code_Cpu_Rocket_Sparkles_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        _barrel_optimize_names_Brain_Code_Cpu_Rocket_Sparkles_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _barrel_optimize_names_Brain_Code_Cpu_Rocket_Sparkles_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        _barrel_optimize_names_Brain_Code_Cpu_Rocket_Sparkles_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    ];\n    // Game mechanics\n    const startGame = ()=>{\n        setGameActive(true);\n        setTimeLeft(30);\n        setScore(0);\n        setClickStreak(0);\n        setMultiplier(1);\n        generateGameElements();\n    };\n    const generateGameElements = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!gameActive) return;\n        const newElement = {\n            id: Date.now(),\n            x: Math.random() * 80 + 10,\n            y: Math.random() * 60 + 20,\n            type: skillIcons[Math.floor(Math.random() * skillIcons.length)].name,\n            points: Math.floor(Math.random() * 50) + 10\n        };\n        setGameElements((prev)=>[\n                ...prev,\n                newElement\n            ]);\n        // Remove element after 3 seconds if not clicked\n        setTimeout(()=>{\n            setGameElements((prev)=>prev.filter((el)=>el.id !== newElement.id));\n        }, 3000);\n    }, [\n        gameActive\n    ]);\n    const handleElementClick = (element)=>{\n        setScore((prev)=>prev + element.points * multiplier);\n        setClickStreak((prev)=>prev + 1);\n        setGameElements((prev)=>prev.filter((el)=>el.id !== element.id));\n        // Create particle effect\n        const newParticles = Array.from({\n            length: 5\n        }, (_, i)=>({\n                id: Date.now() + i,\n                x: element.x,\n                y: element.y,\n                vx: (Math.random() - 0.5) * 10,\n                vy: (Math.random() - 0.5) * 10\n            }));\n        setParticles((prev)=>[\n                ...prev,\n                ...newParticles\n            ]);\n        // Check for achievements\n        checkAchievements();\n        // Increase multiplier on streak\n        if (clickStreak > 0 && clickStreak % 5 === 0) {\n            setMultiplier((prev)=>Math.min(prev + 0.5, 5));\n        }\n    };\n    const checkAchievements = ()=>{\n        const newAchievements = [];\n        if (score >= 100 && !achievements.includes(\"First Century\")) {\n            newAchievements.push(\"First Century\");\n        }\n        if (clickStreak >= 10 && !achievements.includes(\"Streak Master\")) {\n            newAchievements.push(\"Streak Master\");\n        }\n        if (score >= 500 && !achievements.includes(\"Robot Overlord\")) {\n            newAchievements.push(\"Robot Overlord\");\n        }\n        if (newAchievements.length > 0) {\n            setAchievements((prev)=>[\n                    ...prev,\n                    ...newAchievements\n                ]);\n            setShowAchievement(newAchievements[0]);\n            setTimeout(()=>setShowAchievement(null), 3000);\n        }\n    };\n    // Game timer\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (gameActive && timeLeft > 0) {\n            const timer = setTimeout(()=>setTimeLeft((prev)=>prev - 1), 1000);\n            return ()=>clearTimeout(timer);\n        } else if (timeLeft === 0) {\n            setGameActive(false);\n            setGameElements([]);\n        }\n    }, [\n        gameActive,\n        timeLeft\n    ]);\n    // Generate game elements\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (gameActive) {\n            const interval = setInterval(generateGameElements, 1500);\n            return ()=>clearInterval(interval);\n        }\n    }, [\n        gameActive,\n        generateGameElements\n    ]);\n    // Text rotation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            setCurrentText((prev)=>(prev + 1) % heroTexts.length);\n        }, 3000);\n        return ()=>clearInterval(interval);\n    }, []);\n    // Particle animation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            setParticles((prev)=>prev.filter((p)=>Date.now() - p.id < 1000));\n        }, 100);\n        return ()=>clearInterval(interval);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-dark-800 via-dark-900 to-dark-950 pt-32\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-circuit-pattern opacity-10\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-robocell-yellow/5 via-transparent to-robocell-orange/5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, undefined),\n            gameActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-20 left-4 right-4 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center bg-black/50 backdrop-blur-md rounded-xl p-4 border border-robocell-yellow/30\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-robocell-yellow font-tech text-sm\",\n                                            children: \"SCORE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white font-bold text-xl\",\n                                            children: score.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-robocell-orange font-tech text-sm\",\n                                            children: \"STREAK\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white font-bold text-xl\",\n                                            children: clickStreak\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-electric-amber font-tech text-sm\",\n                                            children: \"MULTIPLIER\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white font-bold text-xl\",\n                                            children: [\n                                                multiplier,\n                                                \"x\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-red-400 font-tech text-sm\",\n                                    children: \"TIME\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white font-bold text-2xl\",\n                                    children: [\n                                        timeLeft,\n                                        \"s\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, undefined),\n            showAchievement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-32 left-1/2 transform -translate-x-1/2 z-30 animate-bounce\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-robocell-yellow to-robocell-orange rounded-xl p-4 border-2 border-white shadow-2xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Code_Cpu_Rocket_Sparkles_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-6 w-6 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white font-tech font-bold\",\n                                children: [\n                                    \"Achievement Unlocked: \",\n                                    showAchievement,\n                                    \"!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                lineNumber: 172,\n                columnNumber: 9\n            }, undefined),\n            gameElements.map((element)=>{\n                const IconComponent = skillIcons.find((icon)=>icon.name === element.type) || _barrel_optimize_names_Brain_Code_Cpu_Rocket_Sparkles_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"];\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute z-10 cursor-pointer transform hover:scale-110 transition-all duration-200\",\n                    style: {\n                        left: \"\".concat(element.x, \"%\"),\n                        top: \"\".concat(element.y, \"%\")\n                    },\n                    onClick: ()=>handleElementClick(element),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 rounded-full bg-gradient-to-br from-robocell-yellow to-robocell-orange flex items-center justify-center animate-pulse hover:animate-bounce shadow-lg shadow-robocell-yellow/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                    className: \"h-8 w-8 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black/80 text-robocell-yellow text-xs px-2 py-1 rounded font-tech opacity-0 group-hover:opacity-100 transition-opacity\",\n                                children: [\n                                    \"+\",\n                                    element.points\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 w-16 h-16 rounded-full bg-robocell-yellow/30 animate-ping\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 13\n                    }, undefined)\n                }, element.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 11\n                }, undefined);\n            }),\n            particles.map((particle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute w-2 h-2 bg-robocell-yellow rounded-full animate-ping z-10\",\n                    style: {\n                        left: \"\".concat(particle.x, \"%\"),\n                        top: \"\".concat(particle.y, \"%\"),\n                        transform: \"translate(\".concat(particle.vx, \"px, \").concat(particle.vy, \"px)\")\n                    }\n                }, particle.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, undefined)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"font-tech text-4xl md:text-6xl lg:text-7xl font-bold text-white leading-tight\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block bg-gradient-to-r from-robocell-yellow via-robocell-orange to-electric-amber bg-clip-text text-transparent\",\n                                            children: \"RoboCell\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"block text-2xl md:text-3xl lg:text-4xl font-normal text-gray-300 mt-2\",\n                                            children: \"NIT Durgapur\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-16 md:h-20 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"font-tech text-xl md:text-3xl lg:text-4xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-electric-yellow via-robocell-orange to-electric-amber animate-pulse\",\n                                        children: heroTexts[currentText]\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"max-w-3xl mx-auto text-lg md:text-xl text-gray-300 leading-relaxed\",\n                            children: \"\\uD83E\\uDD16 Welcome to RoboCell - where engineering students turn wild ideas into reality! Join us for epic robotics projects, Robocon adventures, and mind-blowing tech innovations. Ready to code, build, and conquer? Let's make some robot magic! ✨\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-12 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center items-center space-x-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative group cursor-pointer\",\n                                        onClick: handleRobotClick,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-24 h-24 md:w-32 md:h-32 rounded-full border-4 border-robocell-yellow bg-gradient-to-br from-robocell-yellow via-robocell-orange to-electric-amber flex items-center justify-center group-hover:scale-110 transition-all duration-500 shadow-2xl \".concat(robotClicked ? \"animate-bounce\" : \"animate-pulse\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-3 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 md:w-4 md:h-4 bg-white rounded-full \".concat(robotClicked ? \"animate-ping\" : \"animate-pulse\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                                    lineNumber: 255,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-3 h-3 md:w-4 md:h-4 bg-white rounded-full \".concat(robotClicked ? \"animate-ping\" : \"animate-pulse\"),\n                                                                    style: {\n                                                                        animationDelay: \"0.5s\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                                    lineNumber: 256,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Code_Cpu_Rocket_Sparkles_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            className: \"h-6 w-6 md:h-8 md:w-8 text-white mx-auto \".concat(robotClicked ? \"animate-spin\" : \"animate-bounce\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 w-24 h-24 md:w-32 md:h-32 rounded-full bg-gradient-to-br from-robocell-yellow to-robocell-orange opacity-0 group-hover:opacity-30 group-hover:animate-ping\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            sparkles.map((sparkle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Code_Cpu_Rocket_Sparkles_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"absolute h-4 w-4 text-robocell-yellow animate-ping\",\n                                                    style: {\n                                                        left: \"50%\",\n                                                        top: \"50%\",\n                                                        transform: \"translate(\".concat(sparkle.x, \"px, \").concat(sparkle.y, \"px)\"),\n                                                        animationDuration: \"1s\"\n                                                    }\n                                                }, sparkle.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, undefined)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-robocell-yellow text-sm font-tech transition-opacity \".concat(robotClicked ? \"opacity-100\" : \"opacity-0 group-hover:opacity-100\"),\n                                                children: robotClicked ? \"\\uD83C\\uDF89 Welcome to RoboCell!\" : \"Click me! \\uD83E\\uDD16\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-0 left-0 w-full h-full pointer-events-none\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 left-1/4 animate-float bg-robocell-yellow/20 backdrop-blur-sm rounded-full px-3 py-1 text-robocell-yellow text-xs font-bold\",\n                                            children: \"\\uD83C\\uDFC6 Robocon 2024\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-8 right-1/4 animate-float bg-robocell-orange/20 backdrop-blur-sm rounded-full px-3 py-1 text-robocell-orange text-xs font-bold\",\n                                            style: {\n                                                animationDelay: \"1s\"\n                                            },\n                                            children: \"⚡ 587+ Members\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-4 left-1/3 animate-float bg-electric-amber/20 backdrop-blur-sm rounded-full px-3 py-1 text-electric-amber text-xs font-bold\",\n                                            style: {\n                                                animationDelay: \"2s\"\n                                            },\n                                            children: \"\\uD83D\\uDE80 30+ Projects\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-6 h-10 border-2 border-robocell-yellow rounded-full flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-1 h-3 bg-gradient-to-b from-robocell-yellow to-robocell-orange rounded-full mt-2 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n        lineNumber: 139,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Hero, \"0THI3MLw8BtGrd6jOuiHUCEYQMM=\");\n_c = Hero;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Hero);\nvar _c;\n$RefreshReg$(_c, \"Hero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/Hero.tsx\n"));

/***/ })

});
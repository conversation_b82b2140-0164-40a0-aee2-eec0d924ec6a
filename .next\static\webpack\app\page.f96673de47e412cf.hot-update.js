"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/LogoShowcase.tsx":
/*!**************************************************!*\
  !*** ./src/components/sections/LogoShowcase.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cog_Cpu_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cog,Cpu,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cog_Cpu_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cog,Cpu,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cog_Cpu_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cog,Cpu,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cog_Cpu_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cog,Cpu,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cog_Cpu_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cog,Cpu,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cog_Cpu_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cog,Cpu,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cog.js\");\n/* harmony import */ var _barrel_optimize_names_Brain_Code_Cog_Cpu_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Brain,Code,Cog,Cpu,Trophy,Wrench,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst InteractiveShowcase = ()=>{\n    _s();\n    const [activeSkill, setActiveSkill] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isHovered, setIsHovered] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const skills = [\n        {\n            icon: _barrel_optimize_names_Brain_Code_Cog_Cpu_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            name: \"Programming\",\n            description: \"Python, C++, Arduino\",\n            color: \"from-robocell-yellow to-robocell-orange\"\n        },\n        {\n            icon: _barrel_optimize_names_Brain_Code_Cog_Cpu_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            name: \"Hardware\",\n            description: \"PCB Design, 3D Printing\",\n            color: \"from-robocell-orange to-electric-amber\"\n        },\n        {\n            icon: _barrel_optimize_names_Brain_Code_Cog_Cpu_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            name: \"AI/ML\",\n            description: \"Computer Vision, Robotics\",\n            color: \"from-electric-amber to-robocell-yellow\"\n        },\n        {\n            icon: _barrel_optimize_names_Brain_Code_Cog_Cpu_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            name: \"Competitions\",\n            description: \"Robocon, Hackathons\",\n            color: \"from-robocell-yellow to-robocell-orange\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            if (!isHovered) {\n                setActiveSkill((prev)=>(prev + 1) % skills.length);\n            }\n        }, 2000);\n        return ()=>clearInterval(interval);\n    }, [\n        isHovered,\n        skills.length\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section-padding bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-20 left-10 animate-float\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Code_Cog_Cpu_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-8 w-8 text-robocell-yellow\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\LogoShowcase.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\LogoShowcase.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-40 right-20 animate-float\",\n                        style: {\n                            animationDelay: \"1s\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Code_Cog_Cpu_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-12 w-12 text-robocell-orange animate-spin-slow\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\LogoShowcase.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\LogoShowcase.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-40 left-20 animate-float\",\n                        style: {\n                            animationDelay: \"2s\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Brain_Code_Cog_Cpu_Trophy_Wrench_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-6 w-6 text-electric-yellow\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\LogoShowcase.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\LogoShowcase.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\LogoShowcase.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto text-center relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"font-tech text-3xl md:text-5xl font-bold text-white mb-4\",\n                            children: [\n                                \"What You'll \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-robocell-yellow to-robocell-orange bg-clip-text text-transparent\",\n                                    children: \"Master\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\LogoShowcase.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\LogoShowcase.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-300 mb-12\",\n                            children: \"Hover over the skills to see what awaits you in RoboCell! \\uD83D\\uDE80\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\LogoShowcase.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-6 mb-12\",\n                            children: skills.map((skill, index)=>{\n                                const IconComponent = skill.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative p-6 rounded-xl cursor-pointer transition-all duration-500 transform hover:scale-105 \".concat(activeSkill === index ? \"bg-gradient-to-br from-robocell-yellow/20 to-robocell-orange/20 border-2 border-robocell-yellow\" : \"bg-dark-800/50 border border-gray-700\"),\n                                    onMouseEnter: ()=>{\n                                        setActiveSkill(index);\n                                        setIsHovered(true);\n                                    },\n                                    onMouseLeave: ()=>setIsHovered(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 rounded-full bg-gradient-to-br \".concat(skill.color, \" flex items-center justify-center mx-auto mb-4 transition-all duration-300 \").concat(activeSkill === index ? \"animate-pulse\" : \"\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                className: \"h-8 w-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\LogoShowcase.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\LogoShowcase.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-tech text-lg font-bold text-white mb-2\",\n                                            children: skill.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\LogoShowcase.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-300\",\n                                            children: skill.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\LogoShowcase.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        activeSkill === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 rounded-xl bg-gradient-to-br from-robocell-yellow/10 to-robocell-orange/10 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\LogoShowcase.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\LogoShowcase.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 17\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\LogoShowcase.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\LogoShowcase.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\LogoShowcase.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\roboCell\\\\src\\\\components\\\\sections\\\\LogoShowcase.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\n_s(InteractiveShowcase, \"W8LZKXWfGQ9puBX2bXWU6Yopb50=\");\n_c = InteractiveShowcase;\n/* harmony default export */ __webpack_exports__[\"default\"] = (InteractiveShowcase);\nvar _c;\n$RefreshReg$(_c, \"InteractiveShowcase\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/LogoShowcase.tsx\n"));

/***/ })

});